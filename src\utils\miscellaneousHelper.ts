export function undefinedOrNullCheck(value?: any) {
    if (value === undefined || value === null) {
        return false;
    }
    return true;
}

export function deepMerge<T>(obj1: T, obj2: T): T {
    if (obj1 === obj2) return obj1 as T;
    if (!obj1 || typeof obj1 !== "object") return obj2 as T;
    if (!obj2 || typeof obj2 !== "object") return obj1 as T;

    if (Array.isArray(obj1) && Array.isArray(obj2)) {
        return obj2.map((item, index) =>
            obj1[index] && typeof obj1[index] === "object" && typeof item === "object"
                ? deepMerge(obj1[index], item)
                : item
        ) as T;
    }

    const result = { ...obj1 } as T;

    for (const key in obj2) {
        if (obj2.hasOwnProperty(key)) {
            const val1 = obj1[key as keyof T];
            const val2 = obj2[key as keyof T];

            result[key as keyof T] =
                val1 && typeof val1 === "object" && val2 && typeof val2 === "object"
                    ? deepMerge(val1, val2)
                    : val2;
        }
    }

    return result;
}