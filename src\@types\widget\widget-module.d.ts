
import { ComponentType } from "react";

declare module "redi-types" {
    export interface IWidgetModule {
        widget: ComponentType<ComponentProps>;
        schema:yup.ObjectSchema<{}, yup.AnyObject, {}, ""> | null;
        defaults: {};
        fields: ComponentType | null;
    }
    export interface RegisterExportableWidget {
        registerWidget?: (widget: ExportableWidget) => void;
        deregisterWidget?: (widgetId: string) => void;
    }
    export interface WidgetProps extends RegisterExportableWidget {
        widget: WidgetDto;
        isLoading: boolean;
        isEditMode: boolean;
        data: QueryResponseModel | null | undefined;
        error: string | null | undefined;
    }
}