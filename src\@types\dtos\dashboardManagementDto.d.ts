import { string } from "yup";

declare module "redi-types" {
  export interface BaseDashboardManagementDto {
        dashboardId: number;
        title: string;
        isShowTitle: boolean;
        dashboardStatusId: number;
        description?: string;
        icon?: string;
        isTemplate: boolean;
        inheritedFromTemplateDashboardId?: number;
        tenantId?: number;
        deleted: boolean;
        publicDashboardUrl?: string;
        publicDashboardStatusId: number;
        sortOrder?: number;
        isDisplayAtRootLevel: boolean;
  }

  export interface GetDashboardManagementDto extends BaseDashboardManagementDto {}

  export interface GetListDashboardManagementDto extends BaseDashboardManagementDto {}
  
  export interface GetDashboardManagementPlusAccessDto extends BaseDashboardManagementDto
  {
      dashboardAccessRoleId: number
  }

  export interface GetDashboardManagementPlusConfigDto extends BaseDashboardManagementDto 
  {
      configJson: string;
      helpText?: string;
      currentVersionNo: number;
  }

  export interface DashboardManagementStatusDto
  {
      dashboardStatusId: number;
      label: string;
  }

  export interface DashboardManagementPublicStatusDto
  {
      publicDashboardStatusId: number;
      label: string;
  }

  export interface DashboardManagementAuditDto
  {
      dashboardId:  number;
      description?: string;
      createdOn: DateTime;
      createdByName?: string;
  }
}