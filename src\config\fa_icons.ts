/*

Add icons here:
    import { faGears } from "@fortawesome/pro-duotone-svg-icons";    
    library.add(faTimes)

Using import:
    import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

Using solid icons:
    <FontAwesomeIcon icon="times" />

Using pro light icons:
    <FontAwesomeIcon icon={['fal', 'gears']} />

Using pro regular icons:
    <FontAwesomeIcon icon={['far', 'gears']} />

Using pro thin icons:
    <FontAwesomeIcon icon={['fat', 'gears']} />

Using pro duotone icons:
    <FontAwesomeIcon icon={['fad', 'gears']} />

Setting duotone styles in CSS:
    --fa-primary-color: red;
    --fa-secondary-color: orange;
    --fa-primary-opacity: 0.8;
    --fa-secondary-opacity: 0.5
 
*/
import { IconDefinition, library } from "@fortawesome/fontawesome-svg-core";
import {
    faBars,
	faArrowDown,
	faArrowUp
} from "@fortawesome/pro-duotone-svg-icons";

const exportedIcons: IconDefinition[] = [
  // Add any icons that are used in exported components here (ie. SideMenu)
  faBars,
  faArrowDown,
  faArrowUp
];

library.add(
  ...exportedIcons
 );

export default exportedIcons; // Export to Container Micro UI