import { Button } from "@mui/material";
import {  Formik } from "formik";
import './styles.scss';
import FormFields from "./FormFields";
import { WidgetDto } from "redi-types";
import * as yup from "yup";
import ChartService from "../../class/chart";
import { useMemo } from "react";

function getSchema() {
    const chart = new ChartService();
    const _schema = yup.object({
        title: yup.string().max(100, "Must be 100 characters or less"),
        //description: yup.string().required('Description is required'),
        caption: yup.string().max(100, "Must be 100 characters or less"),
        configuration: yup.object({ 
            chart: chart.schema as any
        })
    });
    return _schema;
};

function WidgetForm(props: Props) {

    const { initialValues } = props;

    const schema = useMemo(() => getSchema(), []);
    const _initialValues = useMemo(() => {
        return initialValues;
    }, []);

    const submit = (data: WidgetDto) => {
        props.onSave(data);
    };
    
    return (
        <div>

            <Formik<WidgetDto>
                initialValues={_initialValues!}
                enableReinitialize={true}
                validationSchema={schema}
                onSubmit={(data, actions) => {
                    actions.setSubmitting(true); 
                    submit(data);
                }}
            >
            { form => {
                return (
                    <form onSubmit={form.handleSubmit}>
                        <FormFields />
                        <div>
                            <div />
                            <Button variant="contained" type="submit">Save</Button>
                        </div>
                    </form>
                )
            }}
            </Formik>
        </div>
    );
}

export default WidgetForm;

interface Props {
    initialValues: WidgetDto;
    onSave: (dto: WidgetDto) => void;
}