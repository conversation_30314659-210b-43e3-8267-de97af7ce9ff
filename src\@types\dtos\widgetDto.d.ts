import { Layout } from "react-grid-layout";
import { WidgetTypeEnum } from "../../enum/widgetTypeEnum";
import { TitlePositionEnum } from "../../enum/titlePositionEnum";
import { ChartConfigurationDto } from "redi-chart-types";
import { TableConfigurationDto } from "redi-table-types";
import { StatConfigurationDto } from "redi-stat-types";
import { HeroConfigurationDto } from "redi-hero-types";

declare module "redi-types" {

  export interface WidgetDto {
    /** Unique identifier of the Widget */
    id: string;
    /** Reference to Listing when Widget Config JSON was created */
    listingId: number;
    /** Reference to Listing Attribute of Widget Config JSON was created */
    listingAttributeId: number;
    /** Description of the widget, not displayed on the Widget */
    description?: string;
    /** Value at the bottom of the Widget */
    caption?: string;
    /** Title displayed at the top of the Widget */
    title?: string;
    /** Title position */
    titlePositionCode?: TitlePositionEnum;
    /** Title colour */
    titleColour?: string;
    /** Hide title option */
    isTitleHidden?:boolean;
    /** Refresh rate of data for the widget in ms */
    refreshRate?:number;
    /** Widget background colour */
    backgroundColour?: string;
    /** Show the widget in full screen */
    isFullScreen?: boolean;
    /** Show full screen option in editor */
    isFullScreenOptionEnabled?: boolean;
    /** Type of widget component */
    widgetTypeCode: WidgetTypeEnum;
    /** Widget Types Available for this Configuration */
    widgetTypesAvailable: Array<string>;
    /** Widget specific configuration options */
    configuration: ConfigurationDto;
    /** Query requests used in the Data API fetch call by Widget */
    queryRequests: QueryRequests;
    /** Query Request Model Helper fields that help shape the Request model sent to the server */
    queryHelperFields?: QueryRequestModelHelperFields;
    /** Field standard options global */
    fieldStandardOptions?: FieldStandardOptions;
    /** Field Property Overrides, last added will override */
    fieldPropertiesOverrides?: FieldPropertiesOverride[];
    /** Styling properties for the Widget and Card */
    styleProperties?: StyleProperties;
    /** Draft mode enabled */
    isDraftModeEnabled?: boolean;
  }

  /** Query requests used in the Data API fetch call by Widget */
  export interface QueryRequests {
    gauge?: QueryRequestModel;
    iFrame?: QueryRequestModel;
    control?: QueryRequestModel;
    stat?: QueryRequestModel;
    hero?: QueryRequestModel;
    search?: QueryRequestModel;
    map?: QueryRequestModel;
    /** Query Request model used in the Data API fetch call for Table widget */
    chart?: QueryRequestModel;
    /** Query Request model used in the Data API fetch call for Table widget */
    table?: QueryRequestModel;
    /** Query Request model default values used in the Data API fetch call for majority of Widgets */
    general: QueryRequestModel;
  }

  /** Query Request Model Helper fields that help shape the Request model sent to the server */
  export interface QueryRequestModelHelperFields {
      /** Default Dataset Group by Option selected e.g. Summary_ByDay */
      datasetGroupBy: DatasetGroupByOptionDto;
      /** Dataset Group By Options */
      datasetGroupByOptions?: DatasetGroupByOptionDto[];
      /** Default Filter Option  e.g. List of 'TicketTypeName' */
      filterOption?: FilterOptionDto;
      /** Filter values selected to be used in the Query Response Model */
      filtersSelected?: string[];
  }

  /** Widget specific pre-configuration options to switch to when the Widget Type is changed */
  export interface ConfigurationDto {
    chart: ChartConfigurationDto;
    table?: TableConfigurationDto;
    stat?: StatConfigurationDto;
    hero?: HeroConfigurationDto;
  }

  export interface DatasetGroupByOptionDto {
    code: string;
    label: string;
    datasetGroupBy: string;
    /** List of Filter Options e.g. List of 'TicketTypeName' for CustomerSupport_TicketType_ByDay_zendesk */
    filterOptions?: FilterOptionDto[];
  }

  export interface FilterOptionDto {
    code: string;
    label: string;
    datasetGroupBy: string;
  }
}