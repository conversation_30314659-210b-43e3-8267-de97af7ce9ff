.test-1 {
    height: 100%;
}

:global {
/***
*
* Highcharts specific CSS styling
*
****/
.highcharts-plot-lines-999 {
  z-index: 1;
}
.highcharts-plot-line  {
  z-index: 1;
}
.chart-outer {
    max-width: 800px;
    margin: 2em auto;
  }
  
  #container {
    height: 300px;
    margin-top: 2em;
    min-width: 380px;
  }
  
  .highcharts-data-table table {
    border-collapse: collapse;
    border-spacing: 0;
    background: white;
    min-width: 100%;
    margin-top: 10px;
    font-family: sans-serif;
    font-size: 0.9em;
  }
  
  .highcharts-data-table td,
  .highcharts-data-table th,
  .highcharts-data-table caption {
    border: 1px solid silver;
    padding: 0.5em;
  }
  
  .highcharts-data-table tr:nth-child(even),
  .highcharts-data-table thead tr {
    background: #f8f8f8;
  }
  
  .highcharts-data-table tr {
    cursor: pointer;
  }
  
  .highcharts-data-table tr:hover {
    background: #eff;
  }
  
  .highcharts-data-table caption {
    border-bottom: none;
    font-size: 1.1em;
    font-weight: bold;
  }
  
  .highcharts-sort-ascending::after {
    content: " ↓";
  }
  
  .highcharts-sort-descending::after {
    content: " ↑";
  }

  .enable-logo {
    image {
        display: inline;
    }
  }

  .disable-pd-logo {
    image {
        display: none;
    }
  }
  
}