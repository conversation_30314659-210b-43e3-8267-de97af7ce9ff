import { number, string } from "yup";

declare module "redi-types" {
  export interface BaseFolderDto {
    dashboardFolderId: number;
    name: string;
    sortOrder?: number;
    tenantId?: number;
  }

  export interface GetFolderDto extends BaseFolderDto {}

  export interface GetListFolderDto extends BaseFolderDto {}

  export interface FolderWithAccessDto extends BaseFolderCDto
  {
      dashboardId: number;
  }

  export interface BaseFolderDashboardDto
  {
      dashboardFolderDashboardId: number;
      dashboardFolderId: number;
      dashboardId: number;
      sortOrder?: number;
  }

  export interface GetFolderDashboardDto extends BaseFolderDashboardDto { }
}