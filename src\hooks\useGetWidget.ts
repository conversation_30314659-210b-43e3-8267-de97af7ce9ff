import { useMemo } from "react";
import { ChartOptionsDto, WidgetDto } from "redi-types";
import { deepMerge } from "../utils/miscellaneousHelper";

/**
 * Merge chart options and override options if available
 * @param widget 
 * @returns 
 */
function useGetWidget(widget: WidgetDto) {
    const result = useMemo(() => {
        const datasetGroupBySelected = widget.queryHelperFields?.datasetGroupBy;
        if (datasetGroupBySelected 
            && widget?.configuration?.chart?.overrideOptions
            && widget.configuration.chart.overrideOptions.length > 0) {
                const overrideOptions = widget.configuration.chart.overrideOptions.find(x => x.datasetGroupByOptions?.includes(datasetGroupBySelected.code))
                if (overrideOptions) {
                    const dto: WidgetDto = {
                        ...widget,
                        configuration: {
                            ...widget.configuration,
                            chart: {
                                ...widget.configuration.chart,
                                options: deepMerge(widget.configuration.chart.options, overrideOptions as ChartOptionsDto)
                            }
                        }
                    };
                    return dto;
                }
        }
        return widget;
    }, [widget?.configuration?.chart]);

    return result;
}

export default useGetWidget;