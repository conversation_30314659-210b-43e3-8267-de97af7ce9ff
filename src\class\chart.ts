import { ChartConfigurationDto, IWidgetModule, WidgetProps } from "redi-types";
import * as yup from "yup";
import Chart from "../components/Chart/Chart";
import FormFields from "../components/Form/FormFields";
import { ComponentType } from "react";

export default class ChartService implements IWidgetModule {
    get widget():ComponentType<WidgetProps> {
        return Chart;
    }
    get schema(): yup.ObjectSchema<{options: {}}, yup.AnyObject, {options: {}}, ""> | null {
        const formSchema = yup.object({
            options: yup.object({
                chart: yup.object({
                    //title: yup.string().required('Chart title is required'),
                    //subtitle: yup.string().required('Chart subtitle is required'),
                    type: yup.string().required('Chart type is required'),
                })
            })
        });
        return formSchema;
    }
    get defaults(): ChartConfigurationDto {
        const dto: ChartConfigurationDto = {
            isTableEnabled: false,
            options: {
                series: [],
                chart: {
                    title: "",
                    subtitle: "",
                    type: "spline",
                    options3d: { enabled: false }
                },
                xAxis: {
                    title: "",
                    type: "linear",
                    isAxisShared: true
                },
                yAxis: {
                    title: "",
                    type: "linear"
                },
                legend: {
                    enabled: true,
                    align: "center",
                    margin: null
                },
                tooltip: {
                    enabled: true
                },
                dataLabels: {
                    enabled: true,
                    align: "center"
                }
            }
        };
        return dto;
    }
    get fields():ComponentType | null {
        return FormFields;
    }
}