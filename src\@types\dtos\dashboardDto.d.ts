declare module "redi-types" {
  export interface BaseDashboardDto {
        // dashboard table
        dashboardId: number;
        title: string;
        isShowTitle: boolean;
        dashboardStatusId: number;
        description?: string;
        icon?: string;
        isTemplate: boolean;
        inheritedFromTemplateDashboardId?: number;
        tenantId?: number;
        deleted: boolean;
        publicDashboardUrl?: string;
        publicDashboardStatusId: number;
        sortOrder?: number;
        isDisplayAtRootLevel: boolean;

        // dashboard config table
        configJson: string;
        helpText?: string;
        currentVersionNo: number;

        //Deserialised Dashboard Config
        config: DashboardConfigDto;
  }

  export interface GetDashboardDto extends BaseDashboardDto {}

  export interface GetListDashboardDto extends BaseDashboardDto {}
}