﻿import Highcharts, { Chart } from 'highcharts';
//https://www.highcharts.com/demo/highcharts/synchronized-charts

export class ChartSyncCrosshairs {

    //private _charts:Chart[] = [];

    constructor() {}

    /** clears all data from charts */
    initialise() {
        //this._charts = [];
    }

    /** Enables hi-charts to have cross hairs (a vertical line) synchronised across many charts. */
    addCrossHairsToChart(chart: Chart, event: EventTarget | null) {
        let container = chart.container;
        let offsetTop = container.offsetTop;
        let x = 0;
        let y = 0;

        //this._charts.push(chart);

        container.addEventListener('mousemove', (evt:MouseEvent | PointerEvent) => {

            x = evt.offsetX - chart.plotLeft;// - offset.left;
            y = evt.clientY - chart.plotTop - offsetTop;

            const charts = Highcharts.charts;
            if (charts === undefined) { return; }
            for (let i = 0, len = charts.length; i < len; i++) {
                const _chart = charts[i];
                //Compare similar xAxis's between charts to sync cursors
                if (_chart === undefined || _chart.xAxis[0].options.id !== chart.xAxis[0].options.id) { continue; }
                //remove old plot line and draw new plot line (crosshair) for this chart
                const xAxis1 = _chart.xAxis[0];
 
                // Get the hovered point
                const points:Highcharts.Point[] = [];
                for (const series of _chart.series) {
                    const point = series.searchPoint(evt as PointerEvent, true);
                    if (point) {
                        points.push(point);
                    }
                }
                if (points.length > 0) {
                    _chart.tooltip.refresh(points);
                }
                xAxis1.removePlotLine("myPlotLineId");
                if (chart.xAxis && chart.xAxis[0] && (chart.xAxis[0] as any).hasOwnProperty('translate')) {
                    xAxis1.addPlotLine({
                        value: (chart.xAxis[0] as any).translate(x, true),
                        width: 1,
                        color: '#009FC7',                
                        id: "myPlotLineId",
                        zIndex: 1,
                    });
                }
            } 
        });
    }  
}