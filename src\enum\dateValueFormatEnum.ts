export enum DateValueFormatEnum {
	Day = "Day",
	DayAbbreviation = "DayAbbreviation",
	DayFulllName = "DayFulllName",
	MonthFullName = "MonthFullName",
	MonthFullNameYear = "MonthFullNameYear",
	Year = "Year",
	DayMonth = "DayMonth",
	DayMonthYear = "DayMonthYear",
	Time = "Time",
	TimeAndSecond = "TimeAndSecond",
	Datetime = "Datetime",
	DatetimeYear = "DatetimeYear",
	ReverseDatetime = "ReverseDatetime",
	ReverseDatetimeYear = "ReverseDatetimeYear"
};