declare module "query-redi-types" {
    
	export interface RequestField
    {
        name:string;
        /** Avg, <PERSON>, <PERSON>, <PERSON><PERSON>, Count */
        aggregateFunction?: string;
		/** Condition that can be applied to AggregateFunction. eg. Age > 10. */
        aggregateCondition?: string;
		/** Identifies the field type. number, datetime, string. */
		typeCode: string;
        /** When true will ensure all Queries have the same number of rows */
        alignAllQueries?: boolean;
        /** Avg, <PERSON>, <PERSON>, Sum, Count */
        aggregateFunction?: string;
        multiplier?: number;
        convertToInt?: boolean;
        /** Name is processed as a Regex pattern and queries all matching ADX columns */
        isDynamic?: boolean;
    }

	export interface QueryRequestModel {
		/** A single request can have multiple queries. */
        queries: QueryRequest[];
		/** Optional predefined date range. This will override FromDate and ToDate if specified */
        dateRange?:DateRangeEnum;
        /** Optional FromDate  */
        fromDate?:string;
        /** Optional ToDate */
        toDate?:string;
        /** Connector integration e.g. Zendesk */
        connector?: string;
        /** Dataset source e.g. CustomerSupport */
        dataset?: string;
        /** Optional Iana TimeZone. Tenant TimeZone used if this is not passed in. */
        ianaTimeZone?: string;
        /** Set to true to have the Database Query string returned for each query. For debugging purposes only. */
        returnDbQueryString?: boolean;
	}

	export interface QueryRequest
    {
		/** Simple string to Id the query's response. Can simply be A, B, C, etc. */
        queryId: string;
        /** Dataset Group by Option selected e.g. Summary_ByDay */
        datasetGroupBy: string;
        filters: string[];
        fields: RequestField[];
		/** Data to be returned in table data format. e.g. [[1, "Name", "Description", 10], [2, "Name2", "Description2", 20]] */
		isTableData?: boolean;
		/** Summarise / Group By comma separated list if fields */
        summariseBy?:string;
		/** The maximum number of records to return. */
        limit?: number;
        /** Skip x rows when returning result. Useful for paging or infinite scroll applications */
        offset?: number;
		/**
		 * The interval to return data at. Valid values
         *	SECnnnn - Seconds from 1 to 9999 seconds
         *	MINnnnn - Minutes from 1 to 9999 minutes
         *	HRSnnnn - Hours
         *	DAYnnnn - Days from 1 to 9999 days
         *	MTHnn - Months from 1 to 12 months
         *	YRSnn - Years from 1 to 10 years
		 */
        interval?: string;
		/** Comma separated list of fields to sort the reponse by. Suffix with a - (minus) to sort descending. */
        orderBy?: string;
        /** Set the amount of time in seconds the response to this query can be cached on the server. */
        cachePeriodSeconds?: number;
		/** Optional predefined date range. This will override FromDate and ToDate if specified */
        dateRange?:DateRangeEnum;
        /** Optional FromDate  */
        fromDate?:Date;
        /** Optional ToDate */
        toDate?:Date;
        /** Optional - when set this will cause the date range to be auto calculated based on the date range in the other queries */
        previousPeriod?:PreviousPeriodEnum;
        /** Optional - defaults to false, causes query to separate rows by columns denoted in the enforceUniqueRowsBy property */
        enforceUniqueRows?: boolean;
        /** Optional - only used if enforceUniqueRows is true. Set to comma separated column names to force ADX to separate rows by distinct column values */
        enforceUniqueRowsBy?: string;
        /** Marks query as dynamic, column name is parsed as regex to match all ADX columns */
        isDynamic?: boolean;
    }

	export interface QueryResponseModel {
		/** A single request can have multiple queries. */
		results: Record<string, QueryResponse>;
        /** The FromDate used as a parameter for the queries */
        fromDate?: Date;
        /** The ToDate used as a parameter for the queries */
        toDate?: Date;
        /** The IanaTimeZone used as a parameter for the queries */
        ianaTimeZone?: string;
        /** The start day of week used for the queries */
        startOfWeek?: string;
        /** The Financial Year start day/month used for the queries */
        financialYearStart?: string;
	}

	export interface QueryResponse {
		/**
		 * 200 - data return
		 * 500 - system error
		 * 201 - no data
		 */
		statusCode: number;
		/** A list of response value arrays. There will be a separate values array for each returned field. The order of these corresponds to the Fields list. */
		values?: any[][];
		/** A list of fields returned in the response. The order of these corresponds to the Values list. */
		fields: ResponseField[];
		/** Simple character Id that can be matched against the requests */
		queryId: string;
        /**If the request failed the error details related to the error. Only returned in non-live environments. */
        errorDetail?: string;
        /** The FromDate used as a parameter for the queries  */
        fromDate?:Date;
        /**  Returned when ReturnDbQueryString is true. This is the Database query that was executed. */
        toDate?:Date;
        /** Returned when ReturnDbQueryString is true. This is the Database query that was executed. */
        databaseQueryString?: string;
        /** True if this query response is the result of a dynamically created query request */
        isDynamicResult?: boolean;
	}

	export interface ResponseField {
		name: string;
		/** Identifies the field type. number, datetime, string. */
		typeCode: string;
        /** The name that should be shown in the front-end. */
        displayName?: string;
        /** Specifies the units. Can be any valid type of unit.eg. percent, count, etc  */
        unit?: string;
        /** A Detail description of the field. Can be provided to the user as additional detail. */
        detailDescription?: string;
	}
}