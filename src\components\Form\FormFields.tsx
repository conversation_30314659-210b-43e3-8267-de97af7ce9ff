import { Checkbox, FormControlLabel, MenuItem, TextField } from "@mui/material";
import { Field, FieldProps, useFormikContext } from "formik";
import { WidgetDto } from "redi-types";
import './styles.scss';
import FieldColourPicker from "./FieldColourPicker/FieldColourPicker";

function FormFields(props: Props) {

    const { values, touched, errors, setFieldValue } = useFormikContext<WidgetDto>();

    return (
        <>
            <div styleName="form-grid">
                {/* <Field
                    label="Title"
                    id="chart-title"
                    name={`configuration.chart.options.chart.title`}
                    placeholder="Title"
                    as={TextField}
                    error={touched.configuration?.chart?.options?.chart && touched.configuration.chart.options.chart.title && errors.configuration?.chart?.options?.chart && Boolean(errors.configuration.chart.options.chart.title)}
                    helperText={touched.configuration?.chart?.options?.chart && touched.configuration.chart.options.chart.title && errors.configuration?.chart?.options?.chart && errors.configuration.chart.options.chart.title}
                />
                <Field
                    label="Subtitle"
                    id="chart-subtitle"
                    name={`configuration.chart.options.chart.subtitle`}
                    placeholder="Subsubtitle"
                    as={TextField}
                    error={touched.configuration?.chart?.options?.chart && touched.configuration.chart.options.chart.subtitle && errors.configuration?.chart?.options?.chart && Boolean(errors.configuration.chart.options.chart.subtitle)}
                    helperText={touched.configuration?.chart?.options?.chart && touched.configuration.chart.options.chart.subtitle && errors.configuration?.chart?.options?.chart && errors.configuration.chart.options.chart.subtitle}
                /> */}
                <Field
                    select
                    label="Type"
                    id="chart-type"
                    name={`configuration.chart.options.chart.type`}
                    placeholder="Graph Style"
                    as={TextField}
                    error={touched.configuration?.chart?.options?.chart && touched.configuration.chart.options.chart.type && errors.configuration?.chart?.options?.chart && Boolean(errors.configuration.chart.options.chart.type)}
                    helperText={touched.configuration?.chart?.options?.chart && touched.configuration.chart.options.chart.type && errors.configuration?.chart?.options?.chart && errors.configuration.chart.options.chart.type}
                >
                    <MenuItem key={"bar"} value={"bar"}>Bar</MenuItem>
                    <MenuItem key={"column"} value={"column"}>Column</MenuItem>
                    <MenuItem key={"scatter"} value={"scatter"}>Scatter</MenuItem>
                    <MenuItem key={"pie"} value={"pie"}>Pie</MenuItem>
                    <MenuItem key={"line"} value={"line"}>Line</MenuItem>
                    <MenuItem key={"spline"} value={"spline"}>Spline</MenuItem>
                    <MenuItem key={"area"} value={"area"}>Area</MenuItem>
                </Field>
                <Field name="configuration.chart.options.chart.options3d.enabled">
                {({ field }: FieldProps<boolean>) => (
                    <FormControlLabel
                    control={
                        <Checkbox
                            {...field}
                            value={field.value === true}
                            checked={field.value === true}
                            onChange={(e) => {
                                field.onChange(e);
                            }}
                        />
                    }
                    label="Show as 3D"
                    />
                )}
                </Field>
                <Field
                    id="chart-table-enabled"
                    type="checkbox"
                    name="configuration.chart.isTableEnabled"
                    as={FormControlLabel}
                    control={<Checkbox />}
                    label="Show data table below chart"
                />
            </div>
        </>
    );
}

export default FormFields;

interface Props {}