import { TimeRange, default as DateTime } from "./dateTime";
import * as datefns from "date-fns";


const defaultFormat = "dd MMM yyyy";

// eslint-disable-next-line complexity
export function formatTimeRange(range: TimeRange, format?: string) {
	if (range.endDate && range.startDate) {
		// move the end range to the start of the next day so diffs are correct
		const endAdjusted = DateTime.startOf("day", DateTime.add("hour", range.endDate, 1));
		const diff = DateTime.diff("day", range.startDate, endAdjusted);

		if (diff === 1) {
			//is day
			const nowDiff = DateTime.diff("day", range.startDate, DateTime.startOf("day"));
			return nowDiff === 0
				? "Today"
				: nowDiff === 1
				? "Yesterday"
				: nowDiff === -1
				? "Tomorrow"
				: DateTime.format(range.startDate, format || defaultFormat);
		} else if (diff === 7) {
			// locale inspecific week check
			if (
				DateTime.startOf("week", range.startDate).getTime() === range.startDate.getTime() &&
				DateTime.endOf("week", range.endDate).getTime() === range.endDate.getTime()
			) {
				const thisWeek = DateTime.startOf("week");
				const weekDiff = DateTime.diff("week", range.startDate, thisWeek);
				return weekDiff === 0
					? "This week"
					: weekDiff === 1
					? "Last week"
					: weekDiff === -1
					? "Next week"
					: `${Math.abs(weekDiff)} weeks ${weekDiff > 0 ? "ago" : "later"}`;
			}
		} else if (diff > 27 && diff < 32) {
			// possible month
			if (
				DateTime.format(range.startDate, "d") === "1" &&
				DateTime.endOf("month", range.endDate).getTime() === range.endDate.getTime()
			) {
				//is month
				const thismonth = DateTime.startOf("month");
				const monthDiff = DateTime.diff("month", range.startDate, thismonth);
				return monthDiff === 0
					? "This month"
					: monthDiff === 1
					? "Last month"
					: monthDiff === -1
					? "Next month"
					: `${Math.abs(monthDiff)} months ${monthDiff > 0 ? "ago" : "later"}`;
			}
		} else if (diff === 365 || diff === 366) {
			// possible year
			const thisYear = DateTime.startOf("year");
			const diff = DateTime.diff("day", range.startDate, thisYear);
			if (diff === 0) {
				const diffYear = DateTime.diff("year", range.startDate, thisYear);
				return diffYear === 0
					? "This year"
					: diffYear === 1
					? "Last year"
					: diffYear === -1
					? "Next year"
					: `${Math.abs(diffYear)} years ${diffYear > 0 ? "ago" : "later"}`;
			}
		}

		return `${DateTime.format(range.startDate, format || defaultFormat)} - ${DateTime.format(range.endDate, format || defaultFormat)}`;
	} else if (range.endDate) {
		return `Before ${DateTime.format(range.startDate, format || defaultFormat)}`;
	} else if (range.startDate) {
		return `After ${DateTime.format(range.startDate, format || defaultFormat)}`;
	} else {
		return "All Time";
	}
}

// eslint-disable-next-line complexity
export function formatDate(date: Date, format?: string) {
	// move the end range to the start of the next day so diffs are correct
	const diff = datefns.differenceInCalendarDays(date, new Date());
	console.log(diff);
	if (diff === 0) {
		return "Today";
	} else if (diff === -1) {
		return "Yesterday";
	} else if (diff === 1) {
		return "Tomorrow";
	} else if (diff > 0 && diff < 10) {
		return `${diff} days from now`;
	} else if (diff < 0 && diff > -10) {
		return `${Math.abs(diff)} days ago`;
	} else {
		return DateTime.format(date, format || "d MMM yy");
	}
}

export function getFormattedDate () {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}${minutes}${seconds}`;
};
