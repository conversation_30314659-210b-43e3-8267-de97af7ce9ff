import { Layout } from "react-grid-layout";
import { WidgetTypeEnum } from "../../enum/widgetTypeEnum";
import { TitlePositionEnum } from "../../enum/titlePositionEnum";
import { ValueFormatEnum } from "../../enum/valueFormatEnum";

declare module "redi-types" {

  export interface StyleProperties  {
    /** Background colour of the card the Widget component is housed in */
    cardBackgroundColour?: string;
    /** Prominent colour targets the most prominent part of the Widget (e.g. for Charts is the line, bar. For Stat its the background. etc.)*/
    prominentColour?: string;
  }
}