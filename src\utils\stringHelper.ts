const cache = new Map<string, string>();
const cacheLimit = 500;

export default class StringHelper {
    static format(template: string, values: { value: string | number, [key: string]: string | number }): string {
        const cacheKey = template + values.value;
        const fromCache = cache.get(cacheKey);
        if (fromCache) {
            return fromCache;
        }
        const value = template.replace(/{(\w+)}/g, (match, key) => {
            return values.hasOwnProperty(key) ? String(values[key]) : match;
        });
        if (cache.size === cacheLimit) {
            cache.clear();
        }
        cache.set(cacheKey, value);
        return value;
    }
}