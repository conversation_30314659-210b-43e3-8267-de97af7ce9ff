import { number, string } from "yup";

declare module "redi-types" {
  export interface BaseDashboardManagementShareDto {
    dashboardShareId: number;
    dashboardShareExternalId: string;
    dashboardId: number;
    shareLinkUrl: string;
    shareLinkAcceptedAt?: DateTime;
    sharelinkExpiresAt: DateTime;
    viewLinkUrl: string;
    viewLinkExpiresAt: DateTime;
    sharedWithAddress?: string;
    dashboardShareStatusId: number;
    createdOn: DateTime;
    createdByName?: string;
    modifiedOn?: DateTime;
    modifiedByName?: string;
  }

  export interface GetDashboardManagementShareDto extends BaseDashboardManagementShareDto {}

  export interface GetListDashboardManagementShareDto extends BaseDashboardManagementShareDto {}
}