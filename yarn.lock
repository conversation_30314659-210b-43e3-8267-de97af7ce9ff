# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@ampproject/remapping/-/@ampproject/remapping-2.3.0.tgz#ed441b6fa600072520ce18b43d2c8cc8caecc7f4"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.16.0", "@babel/code-frame@^7.23.5", "@babel/code-frame@^7.24.2", "@babel/code-frame@^7.8.3":
  version "7.24.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/code-frame/-/@babel/code-frame-7.24.2.tgz#718b4b19841809a58b29b68cde80bc5e1aa6d9ae"
  integrity sha1-cYtLGYQYCaWLKbaM3oC8Xhqm2a4=
  dependencies:
    "@babel/highlight" "^7.24.2"
    picocolors "^1.0.0"

"@babel/code-frame@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/code-frame/-/@babel/code-frame-7.24.7.tgz#882fd9e09e8ee324e496bd040401c6f046ef4465"
  integrity sha1-iC/Z4J6O4yTklr0EBAHG8EbvRGU=
  dependencies:
    "@babel/highlight" "^7.24.7"
    picocolors "^1.0.0"

"@babel/compat-data@^7.22.6", "@babel/compat-data@^7.23.5", "@babel/compat-data@^7.24.4":
  version "7.24.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/compat-data/-/@babel/compat-data-7.24.4.tgz#6f102372e9094f25d908ca0d34fc74c74606059a"
  integrity sha1-bxAjcukJTyXZCMoNNPx0x0YGBZo=

"@babel/core@^7.12.3", "@babel/core@^7.16.0", "@babel/core@^7.24.4":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/core/-/@babel/core-7.24.5.tgz#15ab5b98e101972d171aeef92ac70d8d6718f06a"
  integrity sha1-FatbmOEBly0XGu75KscNjWcY8Go=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.24.2"
    "@babel/generator" "^7.24.5"
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-module-transforms" "^7.24.5"
    "@babel/helpers" "^7.24.5"
    "@babel/parser" "^7.24.5"
    "@babel/template" "^7.24.0"
    "@babel/traverse" "^7.24.5"
    "@babel/types" "^7.24.5"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/generator/-/@babel/generator-7.24.5.tgz#e5afc068f932f05616b66713e28d0f04e99daeb3"
  integrity sha1-5a/AaPky8FYWtmcT4o0PBOmdrrM=
  dependencies:
    "@babel/types" "^7.24.5"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^2.5.1"

"@babel/generator@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/generator/-/@babel/generator-7.24.7.tgz#1654d01de20ad66b4b4d99c135471bc654c55e6d"
  integrity sha1-FlTQHeIK1mtLTZnBNUcbxlTFXm0=
  dependencies:
    "@babel/types" "^7.24.7"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-annotate-as-pure/-/@babel/helper-annotate-as-pure-7.22.5.tgz#e7f06737b197d580a01edf75d97e2c8be99d3882"
  integrity sha1-5/BnN7GX1YCgHt912X4si+mdOII=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.22.15":
  version "7.22.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-builder-binary-assignment-operator-visitor/-/@babel/helper-builder-binary-assignment-operator-visitor-7.22.15.tgz#5426b109cf3ad47b91120f8328d8ab1be8b0b956"
  integrity sha1-VCaxCc861HuREg+DKNirG+iwuVY=
  dependencies:
    "@babel/types" "^7.22.15"

"@babel/helper-compilation-targets@^7.22.6", "@babel/helper-compilation-targets@^7.23.6":
  version "7.23.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-compilation-targets/-/@babel/helper-compilation-targets-7.23.6.tgz#4d79069b16cbcf1461289eccfbbd81501ae39991"
  integrity sha1-TXkGmxbLzxRhKJ7M+72BUBrjmZE=
  dependencies:
    "@babel/compat-data" "^7.23.5"
    "@babel/helper-validator-option" "^7.23.5"
    browserslist "^4.22.2"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.24.1", "@babel/helper-create-class-features-plugin@^7.24.4", "@babel/helper-create-class-features-plugin@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-create-class-features-plugin/-/@babel/helper-create-class-features-plugin-7.24.5.tgz#7d19da92c7e0cd8d11c09af2ce1b8e7512a6e723"
  integrity sha1-fRnaksfgzY0RwJryzhuOdRKm5yM=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-member-expression-to-functions" "^7.24.5"
    "@babel/helper-optimise-call-expression" "^7.22.5"
    "@babel/helper-replace-supers" "^7.24.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.24.5"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.22.15", "@babel/helper-create-regexp-features-plugin@^7.22.5":
  version "7.22.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-create-regexp-features-plugin/-/@babel/helper-create-regexp-features-plugin-7.22.15.tgz#5ee90093914ea09639b01c711db0d6775e558be1"
  integrity sha1-XukAk5FOoJY5sBxxHbDWd15Vi+E=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    regexpu-core "^5.3.1"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.6.1", "@babel/helper-define-polyfill-provider@^0.6.2":
  version "0.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-define-polyfill-provider/-/@babel/helper-define-polyfill-provider-0.6.2.tgz#18594f789c3594acb24cfdb4a7f7b7d2e8bd912d"
  integrity sha1-GFlPeJw1lKyyTP20p/e30ui9kS0=
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"

"@babel/helper-environment-visitor@^7.22.20":
  version "7.22.20"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-environment-visitor/-/@babel/helper-environment-visitor-7.22.20.tgz#96159db61d34a29dba454c959f5ae4a649ba9167"
  integrity sha1-lhWdth00op26RUyVn1rkpkm6kWc=

"@babel/helper-environment-visitor@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-environment-visitor/-/@babel/helper-environment-visitor-7.24.7.tgz#4b31ba9551d1f90781ba83491dd59cf9b269f7d9"
  integrity sha1-SzG6lVHR+QeBuoNJHdWc+bJp99k=
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-function-name@^7.23.0":
  version "7.23.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-function-name/-/@babel/helper-function-name-7.23.0.tgz#1f9a3cdbd5b2698a670c30d2735f9af95ed52759"
  integrity sha1-H5o829WyaYpnDDDSc1+a+V7VJ1k=
  dependencies:
    "@babel/template" "^7.22.15"
    "@babel/types" "^7.23.0"

"@babel/helper-function-name@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-function-name/-/@babel/helper-function-name-7.24.7.tgz#75f1e1725742f39ac6584ee0b16d94513da38dd2"
  integrity sha1-dfHhcldC85rGWE7gsW2UUT2jjdI=
  dependencies:
    "@babel/template" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-hoist-variables@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-hoist-variables/-/@babel/helper-hoist-variables-7.22.5.tgz#c01a007dac05c085914e8fb652b339db50d823bb"
  integrity sha1-wBoAfawFwIWRTo+2UrM521DYI7s=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-hoist-variables@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-hoist-variables/-/@babel/helper-hoist-variables-7.24.7.tgz#b4ede1cde2fd89436397f30dc9376ee06b0f25ee"
  integrity sha1-tO3hzeL9iUNjl/MNyTdu4GsPJe4=
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-member-expression-to-functions@^7.23.0", "@babel/helper-member-expression-to-functions@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-member-expression-to-functions/-/@babel/helper-member-expression-to-functions-7.24.5.tgz#5981e131d5c7003c7d1fa1ad49e86c9b097ec475"
  integrity sha1-WYHhMdXHADx9H6GtSehsmwl+xHU=
  dependencies:
    "@babel/types" "^7.24.5"

"@babel/helper-module-imports@^7.16.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-module-imports/-/@babel/helper-module-imports-7.24.7.tgz#f2f980392de5b84c3328fc71d38bd81bbb83042b"
  integrity sha1-8vmAOS3luEwzKPxx04vYG7uDBCs=
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-module-imports@^7.22.15", "@babel/helper-module-imports@^7.24.1", "@babel/helper-module-imports@^7.24.3":
  version "7.24.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-module-imports/-/@babel/helper-module-imports-7.24.3.tgz#6ac476e6d168c7c23ff3ba3cf4f7841d46ac8128"
  integrity sha1-asR25tFox8I/87o89PeEHUasgSg=
  dependencies:
    "@babel/types" "^7.24.0"

"@babel/helper-module-transforms@^7.23.3", "@babel/helper-module-transforms@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-module-transforms/-/@babel/helper-module-transforms-7.24.5.tgz#ea6c5e33f7b262a0ae762fd5986355c45f54a545"
  integrity sha1-6mxeM/eyYqCudi/VmGNVxF9UpUU=
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-module-imports" "^7.24.3"
    "@babel/helper-simple-access" "^7.24.5"
    "@babel/helper-split-export-declaration" "^7.24.5"
    "@babel/helper-validator-identifier" "^7.24.5"

"@babel/helper-optimise-call-expression@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-optimise-call-expression/-/@babel/helper-optimise-call-expression-7.22.5.tgz#f21531a9ccbff644fdd156b4077c16ff0c3f609e"
  integrity sha1-8hUxqcy/9kT90Va0B3wW/ww/YJ4=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.24.0", "@babel/helper-plugin-utils@^7.24.5", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-plugin-utils/-/@babel/helper-plugin-utils-7.24.5.tgz#a924607dd254a65695e5bd209b98b902b3b2f11a"
  integrity sha1-qSRgfdJUplaV5b0gm5i5ArOy8Ro=

"@babel/helper-remap-async-to-generator@^7.22.20":
  version "7.22.20"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-remap-async-to-generator/-/@babel/helper-remap-async-to-generator-7.22.20.tgz#7b68e1cb4fa964d2996fd063723fb48eca8498e0"
  integrity sha1-e2jhy0+pZNKZb9Bjcj+0jsqEmOA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-wrap-function" "^7.22.20"

"@babel/helper-replace-supers@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-replace-supers/-/@babel/helper-replace-supers-7.24.1.tgz#7085bd19d4a0b7ed8f405c1ed73ccb70f323abc1"
  integrity sha1-cIW9GdSgt+2PQFwe1zzLcPMjq8E=
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-member-expression-to-functions" "^7.23.0"
    "@babel/helper-optimise-call-expression" "^7.22.5"

"@babel/helper-simple-access@^7.22.5", "@babel/helper-simple-access@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-simple-access/-/@babel/helper-simple-access-7.24.5.tgz#50da5b72f58c16b07fbd992810be6049478e85ba"
  integrity sha1-UNpbcvWMFrB/vZkoEL5gSUeOhbo=
  dependencies:
    "@babel/types" "^7.24.5"

"@babel/helper-skip-transparent-expression-wrappers@^7.20.0", "@babel/helper-skip-transparent-expression-wrappers@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-skip-transparent-expression-wrappers/-/@babel/helper-skip-transparent-expression-wrappers-7.22.5.tgz#007f15240b5751c537c40e77abb4e89eeaaa8847"
  integrity sha1-AH8VJAtXUcU3xA53q7TonuqqiEc=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-split-export-declaration@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-split-export-declaration/-/@babel/helper-split-export-declaration-7.24.5.tgz#b9a67f06a46b0b339323617c8c6213b9055a78b6"
  integrity sha1-uaZ/BqRrCzOTI2F8jGITuQVaeLY=
  dependencies:
    "@babel/types" "^7.24.5"

"@babel/helper-split-export-declaration@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-split-export-declaration/-/@babel/helper-split-export-declaration-7.24.7.tgz#83949436890e07fa3d6873c61a96e3bbf692d856"
  integrity sha1-g5SUNokOB/o9aHPGGpbju/aS2FY=
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-string-parser@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-string-parser/-/@babel/helper-string-parser-7.24.1.tgz#f99c36d3593db9540705d0739a1f10b5e20c696e"
  integrity sha1-+Zw201k9uVQHBdBzmh8QteIMaW4=

"@babel/helper-string-parser@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-string-parser/-/@babel/helper-string-parser-7.24.7.tgz#4d2d0f14820ede3b9807ea5fc36dfc8cd7da07f2"
  integrity sha1-TS0PFIIO3juYB+pfw238jNfaB/I=

"@babel/helper-validator-identifier@^7.22.20", "@babel/helper-validator-identifier@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-validator-identifier/-/@babel/helper-validator-identifier-7.24.5.tgz#918b1a7fa23056603506370089bd990d8720db62"
  integrity sha1-kYsaf6IwVmA1BjcAib2ZDYcg22I=

"@babel/helper-validator-identifier@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-validator-identifier/-/@babel/helper-validator-identifier-7.24.7.tgz#75b889cfaf9e35c2aaf42cf0d72c8e91719251db"
  integrity sha1-dbiJz6+eNcKq9Czw1yyOkXGSUds=

"@babel/helper-validator-option@^7.23.5":
  version "7.23.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-validator-option/-/@babel/helper-validator-option-7.23.5.tgz#907a3fbd4523426285365d1206c423c4c5520307"
  integrity sha1-kHo/vUUjQmKFNl0SBsQjxMVSAwc=

"@babel/helper-wrap-function@^7.22.20":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-wrap-function/-/@babel/helper-wrap-function-7.24.5.tgz#335f934c0962e2c1ed1fb9d79e06a56115067c09"
  integrity sha1-M1+TTAli4sHtH7nXngalYRUGfAk=
  dependencies:
    "@babel/helper-function-name" "^7.23.0"
    "@babel/template" "^7.24.0"
    "@babel/types" "^7.24.5"

"@babel/helpers@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helpers/-/@babel/helpers-7.24.5.tgz#fedeb87eeafa62b621160402181ad8585a22a40a"
  integrity sha1-/t64fur6YrYhFgQCGBrYWFoipAo=
  dependencies:
    "@babel/template" "^7.24.0"
    "@babel/traverse" "^7.24.5"
    "@babel/types" "^7.24.5"

"@babel/highlight@^7.24.2":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/highlight/-/@babel/highlight-7.24.5.tgz#bc0613f98e1dd0720e99b2a9ee3760194a704b6e"
  integrity sha1-vAYT+Y4d0HIOmbKp7jdgGUpwS24=
  dependencies:
    "@babel/helper-validator-identifier" "^7.24.5"
    chalk "^2.4.2"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/highlight@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/highlight/-/@babel/highlight-7.24.7.tgz#a05ab1df134b286558aae0ed41e6c5f731bf409d"
  integrity sha1-oFqx3xNLKGVYquDtQebF9zG/QJ0=
  dependencies:
    "@babel/helper-validator-identifier" "^7.24.7"
    chalk "^2.4.2"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/parser@^7.24.0", "@babel/parser@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/parser/-/@babel/parser-7.24.5.tgz#4a4d5ab4315579e5398a82dcf636ca80c3392790"
  integrity sha1-Sk1atDFVeeU5ioLc9jbKgMM5J5A=

"@babel/parser@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/parser/-/@babel/parser-7.24.7.tgz#9a5226f92f0c5c8ead550b750f5608e766c8ce85"
  integrity sha1-mlIm+S8MXI6tVQt1D1YI52bIzoU=

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/@babel/plugin-bugfix-firefox-class-in-computed-class-key-7.24.5.tgz#4c3685eb9cd790bcad2843900fe0250c91ccf895"
  integrity sha1-TDaF65zXkLytKEOQD+AlDJHM+JU=
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-plugin-utils" "^7.24.5"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.24.1.tgz#b645d9ba8c2bc5b7af50f0fe949f9edbeb07c8cf"
  integrity sha1-tkXZuowrxbevUPD+lJ+e2+sHyM8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.24.1.tgz#da8261f2697f0f41b0855b91d3a20a1fbfd271d3"
  integrity sha1-2oJh8ml/D0GwhVuR06IKH7/ScdM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/plugin-transform-optional-chaining" "^7.24.1"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.24.1.tgz#1181d9685984c91d657b8ddf14f0487a6bab2988"
  integrity sha1-EYHZaFmEyR1le43fFPBIemurKYg=
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-proposal-class-properties@^7.16.0":
  version "7.18.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-class-properties/-/@babel/plugin-proposal-class-properties-7.18.6.tgz#b110f59741895f7ec21a6fff696ec46265c446a3"
  integrity sha1-sRD1l0GJX37CGm//aW7EYmXERqM=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-decorators@^7.16.4":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-decorators/-/@babel/plugin-proposal-decorators-7.24.1.tgz#bab2b9e174a2680f0a80f341f3ec70f809f8bb4b"
  integrity sha1-urK54XSiaA8KgPNB8+xw+An4u0s=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.1"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-decorators" "^7.24.1"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.16.0":
  version "7.18.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-nullish-coalescing-operator/-/@babel/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz#fdd940a99a740e577d6c753ab6fbb43fdb9467e1"
  integrity sha1-/dlAqZp0Dld9bHU6tvu0P9uUZ+E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.16.0":
  version "7.18.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-numeric-separator/-/@babel/plugin-proposal-numeric-separator-7.18.6.tgz#899b14fbafe87f053d2c5ff05b36029c62e13c75"
  integrity sha1-iZsU+6/ofwU9LF/wWzYCnGLhPHU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-optional-chaining@^7.16.0":
  version "7.21.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-optional-chaining/-/@babel/plugin-proposal-optional-chaining-7.21.0.tgz#886f5c8978deb7d30f678b2e24346b287234d3ea"
  integrity sha1-iG9ciXjet9MPZ4suJDRrKHI00+o=
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.16.0":
  version "7.18.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-private-methods/-/@babel/plugin-proposal-private-methods-7.18.6.tgz#5209de7d213457548a98436fa2882f52f4be6bea"
  integrity sha1-UgnefSE0V1SKmENvoogvUvS+a+o=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-private-property-in-object/-/@babel/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz#7844f9289546efa9febac2de4cfe358a050bd703"
  integrity sha1-eET5KJVG76n+usLeTP41igUL1wM=

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-async-generators/-/@babel/plugin-syntax-async-generators-7.8.4.tgz#a983fb1aeb2ec3f6ed042a210f640e90e786fe0d"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-class-properties/-/@babel/plugin-syntax-class-properties-7.12.13.tgz#b5c987274c4a3a82b89714796931a6b53544ae10"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-class-static-block/-/@babel/plugin-syntax-class-static-block-7.14.5.tgz#195df89b146b4b78b3bf897fd7a257c84659d406"
  integrity sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-decorators@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-decorators/-/@babel/plugin-syntax-decorators-7.24.1.tgz#71d9ad06063a6ac5430db126b5df48c70ee885fa"
  integrity sha1-cdmtBgY6asVDDbEmtd9Ixw7ohfo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-dynamic-import/-/@babel/plugin-syntax-dynamic-import-7.8.3.tgz#62bf98b2da3cd21d626154fc96ee5b3cb68eacb3"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-export-namespace-from/-/@babel/plugin-syntax-export-namespace-from-7.8.3.tgz#028964a9ba80dbc094c915c487ad7c4e7a66465a"
  integrity sha1-AolkqbqA28CUyRXEh618TnpmRlo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-flow@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-flow/-/@babel/plugin-syntax-flow-7.24.1.tgz#875c25e3428d7896c87589765fc8b9d32f24bd8d"
  integrity sha1-h1wl40KNeJbIdYl2X8i50y8kvY0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-syntax-import-assertions@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-import-assertions/-/@babel/plugin-syntax-import-assertions-7.24.1.tgz#db3aad724153a00eaac115a3fb898de544e34971"
  integrity sha1-2zqtckFToA6qwRWj+4mN5UTjSXE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-syntax-import-attributes@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-import-attributes/-/@babel/plugin-syntax-import-attributes-7.24.1.tgz#c66b966c63b714c4eec508fcf5763b1f2d381093"
  integrity sha1-xmuWbGO3FMTuxQj89XY7Hy04EJM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-import-meta/-/@babel/plugin-syntax-import-meta-7.10.4.tgz#ee601348c370fa334d2207be158777496521fd51"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-json-strings/-/@babel/plugin-syntax-json-strings-7.8.3.tgz#01ca21b668cd8218c9e640cb6dd88c5412b2c96a"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.23.3", "@babel/plugin-syntax-jsx@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-jsx/-/@babel/plugin-syntax-jsx-7.24.1.tgz#3f6ca04b8c841811dbc3c5c5f837934e0d626c10"
  integrity sha1-P2ygS4yEGBHbw8XF+DeTTg1ibBA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-logical-assignment-operators/-/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz#ca91ef46303530448b906652bac2e9fe9941f699"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-nullish-coalescing-operator/-/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz#167ed70368886081f74b5c36c65a88c03b66d1a9"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-numeric-separator/-/@babel/plugin-syntax-numeric-separator-7.10.4.tgz#b9b070b3e33570cd9fd07ba7fa91c0dd37b9af97"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-object-rest-spread/-/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-optional-catch-binding/-/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-optional-chaining/-/@babel/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-private-property-in-object/-/@babel/plugin-syntax-private-property-in-object-7.14.5.tgz#0dc6671ec0ea22b6e94a1114f857970cd39de1ad"
  integrity sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-top-level-await/-/@babel/plugin-syntax-top-level-await-7.14.5.tgz#c1cfdadc35a646240001f06138247b741c34d94c"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-typescript/-/@babel/plugin-syntax-typescript-7.24.1.tgz#b3bcc51f396d15f3591683f90239de143c076844"
  integrity sha1-s7zFHzltFfNZFoP5AjneFDwHaEQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-unicode-sets-regex/-/@babel/plugin-syntax-unicode-sets-regex-7.18.6.tgz#d49a3b3e6b52e5be6740022317580234a6a47357"
  integrity sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-arrow-functions/-/@babel/plugin-transform-arrow-functions-7.24.1.tgz#2bf263617060c9cc45bcdbf492b8cc805082bf27"
  integrity sha1-K/JjYXBgycxFvNv0krjMgFCCvyc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-async-generator-functions@^7.24.3":
  version "7.24.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-async-generator-functions/-/@babel/plugin-transform-async-generator-functions-7.24.3.tgz#8fa7ae481b100768cc9842c8617808c5352b8b89"
  integrity sha1-j6euSBsQB2jMmELIYXgIxTUri4k=
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-remap-async-to-generator" "^7.22.20"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-transform-async-to-generator@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-async-to-generator/-/@babel/plugin-transform-async-to-generator-7.24.1.tgz#0e220703b89f2216800ce7b1c53cb0cf521c37f4"
  integrity sha1-DiIHA7ifIhaADOexxTywz1IcN/Q=
  dependencies:
    "@babel/helper-module-imports" "^7.24.1"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-remap-async-to-generator" "^7.22.20"

"@babel/plugin-transform-block-scoped-functions@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-block-scoped-functions/-/@babel/plugin-transform-block-scoped-functions-7.24.1.tgz#1c94799e20fcd5c4d4589523bbc57b7692979380"
  integrity sha1-HJR5niD81cTUWJUju8V7dpKXk4A=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-block-scoping@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-block-scoping/-/@babel/plugin-transform-block-scoping-7.24.5.tgz#89574191397f85661d6f748d4b89ee4d9ee69a2a"
  integrity sha1-iVdBkTl/hWYdb3SNS4nuTZ7mmio=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.5"

"@babel/plugin-transform-class-properties@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-class-properties/-/@babel/plugin-transform-class-properties-7.24.1.tgz#bcbf1aef6ba6085cfddec9fc8d58871cf011fc29"
  integrity sha1-vL8a72umCFz93sn8jViHHPAR/Ck=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.1"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-class-static-block@^7.24.4":
  version "7.24.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-class-static-block/-/@babel/plugin-transform-class-static-block-7.24.4.tgz#1a4653c0cf8ac46441ec406dece6e9bc590356a4"
  integrity sha1-GkZTwM+KxGRB7EBt7ObpvFkDVqQ=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.4"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-transform-classes@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-classes/-/@babel/plugin-transform-classes-7.24.5.tgz#05e04a09df49a46348299a0e24bfd7e901129339"
  integrity sha1-BeBKCd9JpGNIKZoOJL/X6QESkzk=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-plugin-utils" "^7.24.5"
    "@babel/helper-replace-supers" "^7.24.1"
    "@babel/helper-split-export-declaration" "^7.24.5"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-computed-properties/-/@babel/plugin-transform-computed-properties-7.24.1.tgz#bc7e787f8e021eccfb677af5f13c29a9934ed8a7"
  integrity sha1-vH54f44CHsz7Z3r18TwpqZNO2Kc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/template" "^7.24.0"

"@babel/plugin-transform-destructuring@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-destructuring/-/@babel/plugin-transform-destructuring-7.24.5.tgz#80843ee6a520f7362686d1a97a7b53544ede453c"
  integrity sha1-gIQ+5qUg9zYmhtGpentTVE7eRTw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.5"

"@babel/plugin-transform-dotall-regex@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-dotall-regex/-/@babel/plugin-transform-dotall-regex-7.24.1.tgz#d56913d2f12795cc9930801b84c6f8c47513ac13"
  integrity sha1-1WkT0vEnlcyZMIAbhMb4xHUTrBM=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-duplicate-keys@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-duplicate-keys/-/@babel/plugin-transform-duplicate-keys-7.24.1.tgz#5347a797fe82b8d09749d10e9f5b83665adbca88"
  integrity sha1-U0enl/6CuNCXSdEOn1uDZlrbyog=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-dynamic-import@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-dynamic-import/-/@babel/plugin-transform-dynamic-import-7.24.1.tgz#2a5a49959201970dd09a5fca856cb651e44439dd"
  integrity sha1-KlpJlZIBlw3Qml/KhWy2UeREOd0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-transform-exponentiation-operator@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-exponentiation-operator/-/@babel/plugin-transform-exponentiation-operator-7.24.1.tgz#6650ebeb5bd5c012d5f5f90a26613a08162e8ba4"
  integrity sha1-ZlDr61vVwBLV9fkKJmE6CBYui6Q=
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-export-namespace-from@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-export-namespace-from/-/@babel/plugin-transform-export-namespace-from-7.24.1.tgz#f033541fc036e3efb2dcb58eedafd4f6b8078acd"
  integrity sha1-8DNUH8A24++y3LWO7a/U9rgHis0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-transform-flow-strip-types@^7.16.0":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-flow-strip-types/-/@babel/plugin-transform-flow-strip-types-7.24.1.tgz#fa8d0a146506ea195da1671d38eed459242b2dcc"
  integrity sha1-+o0KFGUG6hldoWcdOO7UWSQrLcw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-flow" "^7.24.1"

"@babel/plugin-transform-for-of@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-for-of/-/@babel/plugin-transform-for-of-7.24.1.tgz#67448446b67ab6c091360ce3717e7d3a59e202fd"
  integrity sha1-Z0SERrZ6tsCRNgzjcX59OlniAv0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"

"@babel/plugin-transform-function-name@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-function-name/-/@babel/plugin-transform-function-name-7.24.1.tgz#8cba6f7730626cc4dfe4ca2fa516215a0592b361"
  integrity sha1-jLpvdzBibMTf5MovpRYhWgWSs2E=
  dependencies:
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-json-strings@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-json-strings/-/@babel/plugin-transform-json-strings-7.24.1.tgz#08e6369b62ab3e8a7b61089151b161180c8299f7"
  integrity sha1-COY2m2KrPop7YQiRUbFhGAyCmfc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-transform-literals@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-literals/-/@babel/plugin-transform-literals-7.24.1.tgz#0a1982297af83e6b3c94972686067df588c5c096"
  integrity sha1-ChmCKXr4Pms8lJcmhgZ99YjFwJY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-logical-assignment-operators@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-logical-assignment-operators/-/@babel/plugin-transform-logical-assignment-operators-7.24.1.tgz#719d8aded1aa94b8fb34e3a785ae8518e24cfa40"
  integrity sha1-cZ2K3tGqlLj7NOOnha6FGOJM+kA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-transform-member-expression-literals@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-member-expression-literals/-/@babel/plugin-transform-member-expression-literals-7.24.1.tgz#896d23601c92f437af8b01371ad34beb75df4489"
  integrity sha1-iW0jYByS9DeviwE3GtNL63XfRIk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-modules-amd@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-modules-amd/-/@babel/plugin-transform-modules-amd-7.24.1.tgz#b6d829ed15258536977e9c7cc6437814871ffa39"
  integrity sha1-ttgp7RUlhTaXfpx8xkN4FIcf+jk=
  dependencies:
    "@babel/helper-module-transforms" "^7.23.3"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-modules-commonjs@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-modules-commonjs/-/@babel/plugin-transform-modules-commonjs-7.24.1.tgz#e71ba1d0d69e049a22bf90b3867e263823d3f1b9"
  integrity sha1-5xuh0NaeBJoiv5Czhn4mOCPT8bk=
  dependencies:
    "@babel/helper-module-transforms" "^7.23.3"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-simple-access" "^7.22.5"

"@babel/plugin-transform-modules-systemjs@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-modules-systemjs/-/@babel/plugin-transform-modules-systemjs-7.24.1.tgz#2b9625a3d4e445babac9788daec39094e6b11e3e"
  integrity sha1-K5Ylo9TkRbq6yXiNrsOQlOaxHj4=
  dependencies:
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-module-transforms" "^7.23.3"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-validator-identifier" "^7.22.20"

"@babel/plugin-transform-modules-umd@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-modules-umd/-/@babel/plugin-transform-modules-umd-7.24.1.tgz#69220c66653a19cf2c0872b9c762b9a48b8bebef"
  integrity sha1-aSIMZmU6Gc8sCHK5x2K5pIuL6+8=
  dependencies:
    "@babel/helper-module-transforms" "^7.23.3"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-named-capturing-groups-regex@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-named-capturing-groups-regex/-/@babel/plugin-transform-named-capturing-groups-regex-7.22.5.tgz#67fe18ee8ce02d57c855185e27e3dc959b2e991f"
  integrity sha1-Z/4Y7ozgLVfIVRheJ+PclZsumR8=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-new-target@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-new-target/-/@babel/plugin-transform-new-target-7.24.1.tgz#29c59988fa3d0157de1c871a28cd83096363cc34"
  integrity sha1-KcWZiPo9AVfeHIcaKM2DCWNjzDQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-nullish-coalescing-operator@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-nullish-coalescing-operator/-/@babel/plugin-transform-nullish-coalescing-operator-7.24.1.tgz#0cd494bb97cb07d428bd651632cb9d4140513988"
  integrity sha1-DNSUu5fLB9QovWUWMsudQUBROYg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-transform-numeric-separator@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-numeric-separator/-/@babel/plugin-transform-numeric-separator-7.24.1.tgz#5bc019ce5b3435c1cadf37215e55e433d674d4e8"
  integrity sha1-W8AZzls0NcHK3zchXlXkM9Z01Og=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-transform-object-rest-spread@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-object-rest-spread/-/@babel/plugin-transform-object-rest-spread-7.24.5.tgz#f91bbcb092ff957c54b4091c86bda8372f0b10ef"
  integrity sha1-+Ru8sJL/lXxUtAkchr2oNy8LEO8=
  dependencies:
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-plugin-utils" "^7.24.5"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.24.5"

"@babel/plugin-transform-object-super@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-object-super/-/@babel/plugin-transform-object-super-7.24.1.tgz#e71d6ab13483cca89ed95a474f542bbfc20a0520"
  integrity sha1-5x1qsTSDzKie2VpHT1Qrv8IKBSA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-replace-supers" "^7.24.1"

"@babel/plugin-transform-optional-catch-binding@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-optional-catch-binding/-/@babel/plugin-transform-optional-catch-binding-7.24.1.tgz#92a3d0efe847ba722f1a4508669b23134669e2da"
  integrity sha1-kqPQ7+hHunIvGkUIZpsjE0Zp4to=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-transform-optional-chaining@^7.24.1", "@babel/plugin-transform-optional-chaining@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-optional-chaining/-/@babel/plugin-transform-optional-chaining-7.24.5.tgz#a6334bebd7f9dd3df37447880d0bd64b778e600f"
  integrity sha1-pjNL69f53T3zdEeIDQvWS3eOYA8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-transform-parameters@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-parameters/-/@babel/plugin-transform-parameters-7.24.5.tgz#5c3b23f3a6b8fed090f9b98f2926896d3153cc62"
  integrity sha1-XDsj86a4/tCQ+bmPKSaJbTFTzGI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.5"

"@babel/plugin-transform-private-methods@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-private-methods/-/@babel/plugin-transform-private-methods-7.24.1.tgz#a0faa1ae87eff077e1e47a5ec81c3aef383dc15a"
  integrity sha1-oPqhrofv8Hfh5HpeyBw67zg9wVo=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.1"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-private-property-in-object@7.24.5", "@babel/plugin-transform-private-property-in-object@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-private-property-in-object/-/@babel/plugin-transform-private-property-in-object-7.24.5.tgz#f5d1fcad36e30c960134cb479f1ca98a5b06eda5"
  integrity sha1-9dH8rTbjDJYBNMtHnxypilsG7aU=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-create-class-features-plugin" "^7.24.5"
    "@babel/helper-plugin-utils" "^7.24.5"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-transform-property-literals@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-property-literals/-/@babel/plugin-transform-property-literals-7.24.1.tgz#d6a9aeab96f03749f4eebeb0b6ea8e90ec958825"
  integrity sha1-1qmuq5bwN0n07r6wtuqOkOyViCU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-react-constant-elements@^7.12.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-react-constant-elements/-/@babel/plugin-transform-react-constant-elements-7.24.1.tgz#d493a0918b9fdad7540f5afd9b5eb5c52500d18d"
  integrity sha1-1JOgkYuf2tdUD1r9m161xSUA0Y0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-react-display-name@^7.16.0", "@babel/plugin-transform-react-display-name@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-react-display-name/-/@babel/plugin-transform-react-display-name-7.24.1.tgz#554e3e1a25d181f040cf698b93fd289a03bfdcdb"
  integrity sha1-VU4+GiXRgfBAz2mLk/0omgO/3Ns=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-react-jsx-development@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-react-jsx-development/-/@babel/plugin-transform-react-jsx-development-7.22.5.tgz#e716b6edbef972a92165cd69d92f1255f7e73e87"
  integrity sha1-5xa27b75cqkhZc1p2S8SVffnPoc=
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.22.5"

"@babel/plugin-transform-react-jsx@^7.22.5", "@babel/plugin-transform-react-jsx@^7.23.4":
  version "7.23.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-react-jsx/-/@babel/plugin-transform-react-jsx-7.23.4.tgz#393f99185110cea87184ea47bcb4a7b0c2e39312"
  integrity sha1-OT+ZGFEQzqhxhOpHvLSnsMLjkxI=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-module-imports" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-jsx" "^7.23.3"
    "@babel/types" "^7.23.4"

"@babel/plugin-transform-react-pure-annotations@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-react-pure-annotations/-/@babel/plugin-transform-react-pure-annotations-7.24.1.tgz#c86bce22a53956331210d268e49a0ff06e392470"
  integrity sha1-yGvOIqU5VjMSENJo5JoP8G45JHA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-regenerator@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-regenerator/-/@babel/plugin-transform-regenerator-7.24.1.tgz#625b7545bae52363bdc1fbbdc7252b5046409c8c"
  integrity sha1-Ylt1RbrlI2O9wfu9xyUrUEZAnIw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    regenerator-transform "^0.15.2"

"@babel/plugin-transform-reserved-words@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-reserved-words/-/@babel/plugin-transform-reserved-words-7.24.1.tgz#8de729f5ecbaaf5cf83b67de13bad38a21be57c1"
  integrity sha1-jecp9ey6r1z4O2feE7rTiiG+V8E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-runtime@^7.16.4":
  version "7.24.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-runtime/-/@babel/plugin-transform-runtime-7.24.3.tgz#dc58ad4a31810a890550365cc922e1ff5acb5d7f"
  integrity sha1-3FitSjGBCokFUDZcySLh/1rLXX8=
  dependencies:
    "@babel/helper-module-imports" "^7.24.3"
    "@babel/helper-plugin-utils" "^7.24.0"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.1"
    babel-plugin-polyfill-regenerator "^0.6.1"
    semver "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-shorthand-properties/-/@babel/plugin-transform-shorthand-properties-7.24.1.tgz#ba9a09144cf55d35ec6b93a32253becad8ee5b55"
  integrity sha1-upoJFEz1XTXsa5OjIlO+ytjuW1U=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-spread@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-spread/-/@babel/plugin-transform-spread-7.24.1.tgz#a1acf9152cbf690e4da0ba10790b3ac7d2b2b391"
  integrity sha1-oaz5FSy/aQ5NoLoQeQs6x9Kys5E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"

"@babel/plugin-transform-sticky-regex@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-sticky-regex/-/@babel/plugin-transform-sticky-regex-7.24.1.tgz#f03e672912c6e203ed8d6e0271d9c2113dc031b9"
  integrity sha1-8D5nKRLG4gPtjW4CcdnCET3AMbk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-template-literals@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-template-literals/-/@babel/plugin-transform-template-literals-7.24.1.tgz#15e2166873a30d8617e3e2ccadb86643d327aab7"
  integrity sha1-FeIWaHOjDYYX4+LMrbhmQ9Mnqrc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-typeof-symbol@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-typeof-symbol/-/@babel/plugin-transform-typeof-symbol-7.24.5.tgz#703cace5ef74155fb5eecab63cbfc39bdd25fe12"
  integrity sha1-cDys5e90FV+17sq2PL/Dm90l/hI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.5"

"@babel/plugin-transform-typescript@^7.24.1":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-typescript/-/@babel/plugin-transform-typescript-7.24.5.tgz#bcba979e462120dc06a75bd34c473a04781931b8"
  integrity sha1-vLqXnkYhINwGp1vTTEc6BHgZMbg=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-create-class-features-plugin" "^7.24.5"
    "@babel/helper-plugin-utils" "^7.24.5"
    "@babel/plugin-syntax-typescript" "^7.24.1"

"@babel/plugin-transform-unicode-escapes@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-unicode-escapes/-/@babel/plugin-transform-unicode-escapes-7.24.1.tgz#fb3fa16676549ac7c7449db9b342614985c2a3a4"
  integrity sha1-+z+hZnZUmsfHRJ25s0JhSYXCo6Q=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-unicode-property-regex@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-unicode-property-regex/-/@babel/plugin-transform-unicode-property-regex-7.24.1.tgz#56704fd4d99da81e5e9f0c0c93cabd91dbc4889e"
  integrity sha1-VnBP1NmdqB5enwwMk8q9kdvEiJ4=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-unicode-regex@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-unicode-regex/-/@babel/plugin-transform-unicode-regex-7.24.1.tgz#57c3c191d68f998ac46b708380c1ce4d13536385"
  integrity sha1-V8PBkdaPmYrEa3CDgMHOTRNTY4U=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-unicode-sets-regex@^7.24.1":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-unicode-sets-regex/-/@babel/plugin-transform-unicode-sets-regex-7.24.1.tgz#c1ea175b02afcffc9cf57a9c4658326625165b7f"
  integrity sha1-weoXWwKvz/yc9XqcRlgyZiUWW38=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/preset-env@^7.12.1", "@babel/preset-env@^7.15.8", "@babel/preset-env@^7.16.4":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/preset-env/-/@babel/preset-env-7.24.5.tgz#6a9ac90bd5a5a9dae502af60dfc58c190551bbcd"
  integrity sha1-aprJC9WlqdrlAq9g38WMGQVRu80=
  dependencies:
    "@babel/compat-data" "^7.24.4"
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-plugin-utils" "^7.24.5"
    "@babel/helper-validator-option" "^7.23.5"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key" "^7.24.5"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.24.1"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.24.1"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.24.1"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-import-assertions" "^7.24.1"
    "@babel/plugin-syntax-import-attributes" "^7.24.1"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.24.1"
    "@babel/plugin-transform-async-generator-functions" "^7.24.3"
    "@babel/plugin-transform-async-to-generator" "^7.24.1"
    "@babel/plugin-transform-block-scoped-functions" "^7.24.1"
    "@babel/plugin-transform-block-scoping" "^7.24.5"
    "@babel/plugin-transform-class-properties" "^7.24.1"
    "@babel/plugin-transform-class-static-block" "^7.24.4"
    "@babel/plugin-transform-classes" "^7.24.5"
    "@babel/plugin-transform-computed-properties" "^7.24.1"
    "@babel/plugin-transform-destructuring" "^7.24.5"
    "@babel/plugin-transform-dotall-regex" "^7.24.1"
    "@babel/plugin-transform-duplicate-keys" "^7.24.1"
    "@babel/plugin-transform-dynamic-import" "^7.24.1"
    "@babel/plugin-transform-exponentiation-operator" "^7.24.1"
    "@babel/plugin-transform-export-namespace-from" "^7.24.1"
    "@babel/plugin-transform-for-of" "^7.24.1"
    "@babel/plugin-transform-function-name" "^7.24.1"
    "@babel/plugin-transform-json-strings" "^7.24.1"
    "@babel/plugin-transform-literals" "^7.24.1"
    "@babel/plugin-transform-logical-assignment-operators" "^7.24.1"
    "@babel/plugin-transform-member-expression-literals" "^7.24.1"
    "@babel/plugin-transform-modules-amd" "^7.24.1"
    "@babel/plugin-transform-modules-commonjs" "^7.24.1"
    "@babel/plugin-transform-modules-systemjs" "^7.24.1"
    "@babel/plugin-transform-modules-umd" "^7.24.1"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.22.5"
    "@babel/plugin-transform-new-target" "^7.24.1"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.24.1"
    "@babel/plugin-transform-numeric-separator" "^7.24.1"
    "@babel/plugin-transform-object-rest-spread" "^7.24.5"
    "@babel/plugin-transform-object-super" "^7.24.1"
    "@babel/plugin-transform-optional-catch-binding" "^7.24.1"
    "@babel/plugin-transform-optional-chaining" "^7.24.5"
    "@babel/plugin-transform-parameters" "^7.24.5"
    "@babel/plugin-transform-private-methods" "^7.24.1"
    "@babel/plugin-transform-private-property-in-object" "^7.24.5"
    "@babel/plugin-transform-property-literals" "^7.24.1"
    "@babel/plugin-transform-regenerator" "^7.24.1"
    "@babel/plugin-transform-reserved-words" "^7.24.1"
    "@babel/plugin-transform-shorthand-properties" "^7.24.1"
    "@babel/plugin-transform-spread" "^7.24.1"
    "@babel/plugin-transform-sticky-regex" "^7.24.1"
    "@babel/plugin-transform-template-literals" "^7.24.1"
    "@babel/plugin-transform-typeof-symbol" "^7.24.5"
    "@babel/plugin-transform-unicode-escapes" "^7.24.1"
    "@babel/plugin-transform-unicode-property-regex" "^7.24.1"
    "@babel/plugin-transform-unicode-regex" "^7.24.1"
    "@babel/plugin-transform-unicode-sets-regex" "^7.24.1"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.4"
    babel-plugin-polyfill-regenerator "^0.6.1"
    core-js-compat "^3.31.0"
    semver "^6.3.1"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/preset-modules/-/@babel/preset-modules-0.1.6-no-external-plugins.tgz#ccb88a2c49c817236861fee7826080573b8a923a"
  integrity sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@^7.12.5", "@babel/preset-react@^7.14.5", "@babel/preset-react@^7.16.0":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/preset-react/-/@babel/preset-react-7.24.1.tgz#2450c2ac5cc498ef6101a6ca5474de251e33aa95"
  integrity sha1-JFDCrFzEmO9hAabKVHTeJR4zqpU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-validator-option" "^7.23.5"
    "@babel/plugin-transform-react-display-name" "^7.24.1"
    "@babel/plugin-transform-react-jsx" "^7.23.4"
    "@babel/plugin-transform-react-jsx-development" "^7.22.5"
    "@babel/plugin-transform-react-pure-annotations" "^7.24.1"

"@babel/preset-typescript@^7.10.4", "@babel/preset-typescript@^7.16.0":
  version "7.24.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/preset-typescript/-/@babel/preset-typescript-7.24.1.tgz#89bdf13a3149a17b3b2a2c9c62547f06db8845ec"
  integrity sha1-ib3xOjFJoXs7KiycYlR/BtuIRew=
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-validator-option" "^7.23.5"
    "@babel/plugin-syntax-jsx" "^7.24.1"
    "@babel/plugin-transform-modules-commonjs" "^7.24.1"
    "@babel/plugin-transform-typescript" "^7.24.1"

"@babel/regjsgen@^0.8.0":
  version "0.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/regjsgen/-/@babel/regjsgen-0.8.0.tgz#f0ba69b075e1f05fb2825b7fad991e7adbb18310"
  integrity sha1-8LppsHXh8F+yglt/rZkeetuxgxA=

"@babel/runtime@^7.12.5", "@babel/runtime@^7.16.3", "@babel/runtime@^7.21.0", "@babel/runtime@^7.23.9", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.4", "@babel/runtime@^7.8.7":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/runtime/-/@babel/runtime-7.24.5.tgz#230946857c053a36ccc66e1dd03b17dd0c4ed02c"
  integrity sha1-IwlGhXwFOjbMxm4d0DsX3QxO0Cw=
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/runtime@^7.18.3":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/runtime/-/@babel/runtime-7.24.7.tgz#f4f0d5530e8dbdf59b3451b9b3e594b6ba082e12"
  integrity sha1-9PDVUw6NvfWbNFG5s+WUtroILhI=
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/runtime@^7.25.0":
  version "7.25.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/runtime/-/@babel/runtime-7.25.4.tgz#6ef37d678428306e7d75f054d5b1bdb8cf8aa8ee"
  integrity sha1-bvN9Z4QoMG59dfBU1bG9uM+KqO4=
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.22.15", "@babel/template@^7.24.0":
  version "7.24.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/template/-/@babel/template-7.24.0.tgz#c6a524aa93a4a05d66aaf31654258fae69d87d50"
  integrity sha1-xqUkqpOkoF1mqvMWVCWPrmnYfVA=
  dependencies:
    "@babel/code-frame" "^7.23.5"
    "@babel/parser" "^7.24.0"
    "@babel/types" "^7.24.0"

"@babel/template@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/template/-/@babel/template-7.24.7.tgz#02efcee317d0609d2c07117cb70ef8fb17ab7315"
  integrity sha1-Au/O4xfQYJ0sBxF8tw74+xercxU=
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/parser" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/traverse@^7.24.5":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/traverse/-/@babel/traverse-7.24.5.tgz#972aa0bc45f16983bf64aa1f877b2dd0eea7e6f8"
  integrity sha1-lyqgvEXxaYO/ZKofh3st0O6n5vg=
  dependencies:
    "@babel/code-frame" "^7.24.2"
    "@babel/generator" "^7.24.5"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.24.5"
    "@babel/parser" "^7.24.5"
    "@babel/types" "^7.24.5"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/traverse@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/traverse/-/@babel/traverse-7.24.7.tgz#de2b900163fa741721ba382163fe46a936c40cf5"
  integrity sha1-3iuQAWP6dBchujghY/5GqTbEDPU=
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/generator" "^7.24.7"
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-function-name" "^7.24.7"
    "@babel/helper-hoist-variables" "^7.24.7"
    "@babel/helper-split-export-declaration" "^7.24.7"
    "@babel/parser" "^7.24.7"
    "@babel/types" "^7.24.7"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.12.6", "@babel/types@^7.22.15", "@babel/types@^7.22.5", "@babel/types@^7.23.0", "@babel/types@^7.23.4", "@babel/types@^7.24.0", "@babel/types@^7.24.5", "@babel/types@^7.4.4":
  version "7.24.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/types/-/@babel/types-7.24.5.tgz#7661930afc638a5383eb0c4aee59b74f38db84d7"
  integrity sha1-dmGTCvxjilOD6wxK7lm3TzjbhNc=
  dependencies:
    "@babel/helper-string-parser" "^7.24.1"
    "@babel/helper-validator-identifier" "^7.24.5"
    to-fast-properties "^2.0.0"

"@babel/types@^7.24.7":
  version "7.24.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/types/-/@babel/types-7.24.7.tgz#6027fe12bc1aa724cd32ab113fb7f1988f1f66f2"
  integrity sha1-YCf+ErwapyTNMqsRP7fxmI8fZvI=
  dependencies:
    "@babel/helper-string-parser" "^7.24.7"
    "@babel/helper-validator-identifier" "^7.24.7"
    to-fast-properties "^2.0.0"

"@csstools/normalize.css@*":
  version "12.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/normalize.css/-/@csstools/normalize.css-12.1.1.tgz#f0ad221b7280f3fc814689786fd9ee092776ef8f"
  integrity sha1-8K0iG3KA8/yBRol4b9nuCSd2748=

"@csstools/postcss-cascade-layers@^1.1.1":
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-cascade-layers/-/@csstools/postcss-cascade-layers-1.1.1.tgz#8a997edf97d34071dd2e37ea6022447dd9e795ad"
  integrity sha1-ipl+35fTQHHdLjfqYCJEfdnnla0=
  dependencies:
    "@csstools/selector-specificity" "^2.0.2"
    postcss-selector-parser "^6.0.10"

"@csstools/postcss-color-function@^1.1.1":
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-color-function/-/@csstools/postcss-color-function-1.1.1.tgz#2bd36ab34f82d0497cfacdc9b18d34b5e6f64b6b"
  integrity sha1-K9Nqs0+C0El8+s3JsY00teb2S2s=
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-font-format-keywords@^1.0.1":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-font-format-keywords/-/@csstools/postcss-font-format-keywords-1.0.1.tgz#677b34e9e88ae997a67283311657973150e8b16a"
  integrity sha1-Z3s06eiK6ZemcoMxFleXMVDosWo=
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-hwb-function@^1.0.2":
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-hwb-function/-/@csstools/postcss-hwb-function-1.0.2.tgz#ab54a9fce0ac102c754854769962f2422ae8aa8b"
  integrity sha1-q1Sp/OCsECx1SFR2mWLyQiroqos=
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-ic-unit@^1.0.1":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-ic-unit/-/@csstools/postcss-ic-unit-1.0.1.tgz#28237d812a124d1a16a5acc5c3832b040b303e58"
  integrity sha1-KCN9gSoSTRoWpazFw4MrBAswPlg=
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-is-pseudo-class@^2.0.7":
  version "2.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-is-pseudo-class/-/@csstools/postcss-is-pseudo-class-2.0.7.tgz#846ae6c0d5a1eaa878fce352c544f9c295509cd1"
  integrity sha1-hGrmwNWh6qh4/ONSxUT5wpVQnNE=
  dependencies:
    "@csstools/selector-specificity" "^2.0.0"
    postcss-selector-parser "^6.0.10"

"@csstools/postcss-nested-calc@^1.0.0":
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-nested-calc/-/@csstools/postcss-nested-calc-1.0.0.tgz#d7e9d1d0d3d15cf5ac891b16028af2a1044d0c26"
  integrity sha1-1+nR0NPRXPWsiRsWAoryoQRNDCY=
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-normalize-display-values@^1.0.1":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-normalize-display-values/-/@csstools/postcss-normalize-display-values-1.0.1.tgz#15da54a36e867b3ac5163ee12c1d7f82d4d612c3"
  integrity sha1-FdpUo26GezrFFj7hLB1/gtTWEsM=
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-oklab-function@^1.1.1":
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-oklab-function/-/@csstools/postcss-oklab-function-1.1.1.tgz#88cee0fbc8d6df27079ebd2fa016ee261eecf844"
  integrity sha1-iM7g+8jW3ycHnr0voBbuJh7s+EQ=
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-progressive-custom-properties@^1.1.0", "@csstools/postcss-progressive-custom-properties@^1.3.0":
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-progressive-custom-properties/-/@csstools/postcss-progressive-custom-properties-1.3.0.tgz#542292558384361776b45c85226b9a3a34f276fa"
  integrity sha1-VCKSVYOENhd2tFyFImuaOjTydvo=
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-stepped-value-functions@^1.0.1":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-stepped-value-functions/-/@csstools/postcss-stepped-value-functions-1.0.1.tgz#f8772c3681cc2befed695e2b0b1d68e22f08c4f4"
  integrity sha1-+HcsNoHMK+/taV4rCx1o4i8IxPQ=
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-text-decoration-shorthand@^1.0.0":
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-text-decoration-shorthand/-/@csstools/postcss-text-decoration-shorthand-1.0.0.tgz#ea96cfbc87d921eca914d3ad29340d9bcc4c953f"
  integrity sha1-6pbPvIfZIeypFNOtKTQNm8xMlT8=
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-trigonometric-functions@^1.0.2":
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-trigonometric-functions/-/@csstools/postcss-trigonometric-functions-1.0.2.tgz#94d3e4774c36d35dcdc88ce091336cb770d32756"
  integrity sha1-lNPkd0w2013NyIzgkTNst3DTJ1Y=
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-unset-value@^1.0.2":
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-unset-value/-/@csstools/postcss-unset-value-1.0.2.tgz#c99bb70e2cdc7312948d1eb41df2412330b81f77"
  integrity sha1-yZu3DizccxKUjR60HfJBIzC4H3c=

"@csstools/selector-specificity@^2.0.0", "@csstools/selector-specificity@^2.0.2":
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/selector-specificity/-/@csstools/selector-specificity-2.2.0.tgz#2cbcf822bf3764c9658c4d2e568bd0c0cb748016"
  integrity sha1-LLz4Ir83ZMlljE0uVovQwMt0gBY=

"@discoveryjs/json-ext@^0.5.0":
  version "0.5.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@discoveryjs/json-ext/-/@discoveryjs/json-ext-0.5.7.tgz#1d572bfbbe14b7704e0ba0f39b74815b84870d70"
  integrity sha1-HVcr+74Ut3BOC6Dzm3SBW4SHDXA=

"@dr.pogodin/babel-plugin-react-css-modules@^6.9.3":
  version "6.13.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@dr.pogodin/babel-plugin-react-css-modules/-/@dr.pogodin/babel-plugin-react-css-modules-6.13.1.tgz#70eabb5e29eaa89f8f02130a7d058a7c31de6fe1"
  integrity sha1-cOq7XinqqJ+PAhMKfQWKfDHeb+E=
  dependencies:
    "@babel/core" "^7.24.4"
    "@babel/plugin-syntax-jsx" "^7.24.1"
    "@babel/types" "^7.24.0"
    "@dr.pogodin/postcss-modules-parser" "^1.2.9"
    ajv "^8.12.0"
    ajv-keywords "^5.0.0"
    cssesc "^3.0.0"
    loader-utils "^3.2.1"
    postcss-modules-extract-imports "^3.1.0"
    postcss-modules-local-by-default "^4.0.5"
    postcss-modules-scope "^3.2.0"
    postcss-modules-values "^4.0.0"

"@dr.pogodin/postcss-modules-parser@^1.2.9":
  version "1.2.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@dr.pogodin/postcss-modules-parser/-/@dr.pogodin/postcss-modules-parser-1.2.9.tgz#7b32640b322c897cfe508dfe15047624de4cb1be"
  integrity sha1-ezJkCzIsiXz+UI3+FQR2JN5Msb4=
  dependencies:
    icss-utils "^5.1.0"

"@emotion/babel-plugin@^11.11.0":
  version "11.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/babel-plugin/-/@emotion/babel-plugin-11.11.0.tgz#c2d872b6a7767a9d176d007f5b31f7d504bb5d6c"
  integrity sha1-wthytqd2ep0XbQB/WzH31QS7XWw=
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/runtime" "^7.18.3"
    "@emotion/hash" "^0.9.1"
    "@emotion/memoize" "^0.8.1"
    "@emotion/serialize" "^1.1.2"
    babel-plugin-macros "^3.1.0"
    convert-source-map "^1.5.0"
    escape-string-regexp "^4.0.0"
    find-root "^1.1.0"
    source-map "^0.5.7"
    stylis "4.2.0"

"@emotion/cache@^11.11.0":
  version "11.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/cache/-/@emotion/cache-11.11.0.tgz#809b33ee6b1cb1a625fef7a45bc568ccd9b8f3ff"
  integrity sha1-gJsz7mscsaYl/vekW8VozNm48/8=
  dependencies:
    "@emotion/memoize" "^0.8.1"
    "@emotion/sheet" "^1.2.2"
    "@emotion/utils" "^1.2.1"
    "@emotion/weak-memoize" "^0.3.1"
    stylis "4.2.0"

"@emotion/hash@^0.9.1":
  version "0.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/hash/-/@emotion/hash-0.9.1.tgz#4ffb0055f7ef676ebc3a5a91fb621393294e2f43"
  integrity sha1-T/sAVffvZ268OlqR+2ITkylOL0M=

"@emotion/is-prop-valid@^1.2.2":
  version "1.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/is-prop-valid/-/@emotion/is-prop-valid-1.2.2.tgz#d4175076679c6a26faa92b03bb786f9e52612337"
  integrity sha1-1BdQdmecaib6qSsDu3hvnlJhIzc=
  dependencies:
    "@emotion/memoize" "^0.8.1"

"@emotion/memoize@^0.8.1":
  version "0.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/memoize/-/@emotion/memoize-0.8.1.tgz#c1ddb040429c6d21d38cc945fe75c818cfb68e17"
  integrity sha1-wd2wQEKcbSHTjMlF/nXIGM+2jhc=

"@emotion/react@^11.10.6":
  version "11.11.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/react/-/@emotion/react-11.11.4.tgz#3a829cac25c1f00e126408fab7f891f00ecc3c1d"
  integrity sha1-OoKcrCXB8A4SZAj6t/iR8A7MPB0=
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.11.0"
    "@emotion/cache" "^11.11.0"
    "@emotion/serialize" "^1.1.3"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.0.1"
    "@emotion/utils" "^1.2.1"
    "@emotion/weak-memoize" "^0.3.1"
    hoist-non-react-statics "^3.3.1"

"@emotion/serialize@^1.1.2", "@emotion/serialize@^1.1.3", "@emotion/serialize@^1.1.4":
  version "1.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/serialize/-/@emotion/serialize-1.1.4.tgz#fc8f6d80c492cfa08801d544a05331d1cc7cd451"
  integrity sha1-/I9tgMSSz6CIAdVEoFMx0cx81FE=
  dependencies:
    "@emotion/hash" "^0.9.1"
    "@emotion/memoize" "^0.8.1"
    "@emotion/unitless" "^0.8.1"
    "@emotion/utils" "^1.2.1"
    csstype "^3.0.2"

"@emotion/sheet@^1.2.2":
  version "1.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/sheet/-/@emotion/sheet-1.2.2.tgz#d58e788ee27267a14342303e1abb3d508b6d0fec"
  integrity sha1-1Y54juJyZ6FDQjA+Grs9UIttD+w=

"@emotion/styled@^11.10.6":
  version "11.11.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/styled/-/@emotion/styled-11.11.5.tgz#0c5c8febef9d86e8a926e663b2e5488705545dfb"
  integrity sha1-DFyP6++dhuipJuZjsuVIhwVUXfs=
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.11.0"
    "@emotion/is-prop-valid" "^1.2.2"
    "@emotion/serialize" "^1.1.4"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.0.1"
    "@emotion/utils" "^1.2.1"

"@emotion/unitless@^0.8.1":
  version "0.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/unitless/-/@emotion/unitless-0.8.1.tgz#182b5a4704ef8ad91bde93f7a860a88fd92c79a3"
  integrity sha1-GCtaRwTvitkb3pP3qGCoj9kseaM=

"@emotion/use-insertion-effect-with-fallbacks@^1.0.1":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/use-insertion-effect-with-fallbacks/-/@emotion/use-insertion-effect-with-fallbacks-1.0.1.tgz#08de79f54eb3406f9daaf77c76e35313da963963"
  integrity sha1-CN559U6zQG+dqvd8duNTE9qWOWM=

"@emotion/utils@^1.2.1":
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/utils/-/@emotion/utils-1.2.1.tgz#bbab58465738d31ae4cb3dbb6fc00a5991f755e4"
  integrity sha1-u6tYRlc40xrkyz27b8AKWZH3VeQ=

"@emotion/weak-memoize@^0.3.1":
  version "0.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/weak-memoize/-/@emotion/weak-memoize-0.3.1.tgz#d0fce5d07b0620caa282b5131c297bb60f9d87e6"
  integrity sha1-0Pzl0HsGIMqigrUTHCl7tg+dh+Y=

"@fortawesome/fontawesome-common-types@6.5.2":
  version "6.5.2"
  resolved "https://npm.fontawesome.com/@fortawesome/fontawesome-common-types/-/6.5.2/fontawesome-common-types-6.5.2.tgz#eaf2f5699f73cef198454ebc0c414e3688898179"
  integrity sha512-gBxPg3aVO6J0kpfHNILc+NMhXnqHumFxOmjYCFfOiLZfwhnnfhtsdA2hfJlDnj+8PjAs6kKQPenOTKj3Rf7zHw==

"@fortawesome/fontawesome-svg-core@^6.3.0":
  version "6.5.2"
  resolved "https://npm.fontawesome.com/@fortawesome/fontawesome-svg-core/-/6.5.2/fontawesome-svg-core-6.5.2.tgz#4b42de71e196039b0d5ccf88559b8044e3296c21"
  integrity sha512-5CdaCBGl8Rh9ohNdxeeTMxIj8oc3KNBgIeLMvJosBMdslK/UnEB8rzyDRrbKdL1kDweqBPo4GT9wvnakHWucZw==
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.5.2"

"@fortawesome/pro-duotone-svg-icons@^6.3.0":
  version "6.5.2"
  resolved "https://npm.fontawesome.com/@fortawesome/pro-duotone-svg-icons/-/6.5.2/pro-duotone-svg-icons-6.5.2.tgz#dadc9883f20a3b3dca36014ada931151b991acea"
  integrity sha512-U8dWrpPxajix1ijJQI8dWkSRpDPnXS/3/XJnCzQ6RlPLaGtAY7LelsYHM/byKUeHDpqpySfIGN1lLh63PDU9ag==
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.5.2"

"@fortawesome/pro-regular-svg-icons@^6.3.0":
  version "6.5.2"
  resolved "https://npm.fontawesome.com/@fortawesome/pro-regular-svg-icons/-/6.5.2/pro-regular-svg-icons-6.5.2.tgz#8b5f2426b37b60e5432357dd0abe3b07ceb00663"
  integrity sha512-S+XTfbq6CUmEVZzlBhyNEDqFg1wv7jJYoJZVqOpPOLHWjeN5pIpkVcZ3NVHVbhMuhGVtER1lt/h5NTZYsJWmYQ==
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.5.2"

"@fortawesome/pro-solid-svg-icons@^6.3.0":
  version "6.5.2"
  resolved "https://npm.fontawesome.com/@fortawesome/pro-solid-svg-icons/-/6.5.2/pro-solid-svg-icons-6.5.2.tgz#0996a719cad7a5ce02b20b2da2045d590643a6e1"
  integrity sha512-yhKyRyLDJoTSUN8h+oBl+yspfE6ARPRRbLWbWN4qEiOEcULKTtCTqsqovrnLsCB7qbllRwC50ai+RZ7bC1OPaA==
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.5.2"

"@fortawesome/react-fontawesome@^0.2.0":
  version "0.2.0"
  resolved "https://npm.fontawesome.com/@fortawesome/react-fontawesome/-/0.2.0/react-fontawesome-0.2.0.tgz#d90dd8a9211830b4e3c08e94b63a0ba7291ddcf4"
  integrity sha512-uHg75Rb/XORTtVt7OS9WoK8uM276Ufi7gCzshVWkUJbHhh3svsUUeqXerrM96Wm7fRiDzfKRwSoahhMIkGAYHw==
  dependencies:
    prop-types "^15.8.1"

"@jest/schemas@^29.6.3":
  version "29.6.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jest/schemas/-/@jest/schemas-29.6.3.tgz#430b5ce8a4e0044a7e3819663305a7b3091c8e03"
  integrity sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM=
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jest/types@^29.6.3":
  version "29.6.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jest/types/-/@jest/types-29.6.3.tgz#1131f8cf634e7e84c5e77bab12f052af585fba59"
  integrity sha1-ETH4z2NOfoTF53urEvBSr1hfulk=
  dependencies:
    "@jest/schemas" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/gen-mapping/-/@jridgewell/gen-mapping-0.3.5.tgz#dcce6aff74bdf6dad1a95802b69b04a2fcb1fb36"
  integrity sha1-3M5q/3S99trRqVgCtpsEovyx+zY=
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/resolve-uri/-/@jridgewell/resolve-uri-3.1.2.tgz#7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/set-array/-/@jridgewell/set-array-1.2.1.tgz#558fb6472ed16a4c850b889530e6b36438c49280"
  integrity sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=

"@jridgewell/source-map@^0.3.3":
  version "0.3.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/source-map/-/@jridgewell/source-map-0.3.6.tgz#9d71ca886e32502eb9362c9a74a46787c36df81a"
  integrity sha1-nXHKiG4yUC65NiyadKRnh8Nt+Bo=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  version "1.4.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/sourcemap-codec/-/@jridgewell/sourcemap-codec-1.4.15.tgz#d7c6e6755c78567a951e04ab52ef0fd26de59f32"
  integrity sha1-18bmdVx4VnqVHgSrUu8P0m3lnzI=

"@jridgewell/trace-mapping@^0.3.20", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/trace-mapping/-/@jridgewell/trace-mapping-0.3.25.tgz#15f190e98895f3fc23276ee14bc76b675c2e50f0"
  integrity sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@leichtgewicht/ip-codec@^2.0.1":
  version "2.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@leichtgewicht/ip-codec/-/@leichtgewicht/ip-codec-2.0.5.tgz#4fc56c15c580b9adb7dc3c333a134e540b44bfb1"
  integrity sha1-T8VsFcWAua233DwzOhNOVAtEv7E=

"@mui/core-downloads-tracker@^5.16.7":
  version "5.16.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/core-downloads-tracker/-/@mui/core-downloads-tracker-5.16.7.tgz#182a325a520f7ebd75de051fceabfc0314cfd004"
  integrity sha1-GCoyWlIPfr113gUfzqv8AxTP0AQ=

"@mui/icons-material@^5.11.11":
  version "5.15.16"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/icons-material/-/@mui/icons-material-5.15.16.tgz#2fedd7b2bec967a8ac4bcbcf41da769b7508237b"
  integrity sha1-L+3Xsr7JZ6isS8vPQdp2m3UII3s=
  dependencies:
    "@babel/runtime" "^7.23.9"

"@mui/material@^5.15.14":
  version "5.16.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/material/-/@mui/material-5.16.7.tgz#6e814e2eefdaf065a769cecf549c3569e107a50b"
  integrity sha1-boFOLu/a8GWnac7PVJw1aeEHpQs=
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@mui/core-downloads-tracker" "^5.16.7"
    "@mui/system" "^5.16.7"
    "@mui/types" "^7.2.15"
    "@mui/utils" "^5.16.6"
    "@popperjs/core" "^2.11.8"
    "@types/react-transition-group" "^4.4.10"
    clsx "^2.1.0"
    csstype "^3.1.3"
    prop-types "^15.8.1"
    react-is "^18.3.1"
    react-transition-group "^4.4.5"

"@mui/private-theming@^5.16.6":
  version "5.16.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/private-theming/-/@mui/private-theming-5.16.6.tgz#547671e7ae3f86b68d1289a0b90af04dfcc1c8c9"
  integrity sha1-VHZx564/hraNEomguQrwTfzByMk=
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@mui/utils" "^5.16.6"
    prop-types "^15.8.1"

"@mui/styled-engine@^5.16.6":
  version "5.16.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/styled-engine/-/@mui/styled-engine-5.16.6.tgz#60110c106dd482dfdb7e2aa94fd6490a0a3f8852"
  integrity sha1-YBEMEG3Ugt/bfiqpT9ZJCgo/iFI=
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@emotion/cache" "^11.11.0"
    csstype "^3.1.3"
    prop-types "^15.8.1"

"@mui/system@^5.16.7":
  version "5.16.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/system/-/@mui/system-5.16.7.tgz#4583ca5bf3b38942e02c15a1e622ba869ac51393"
  integrity sha1-RYPKW/OziULgLBWh5iK6hprFE5M=
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@mui/private-theming" "^5.16.6"
    "@mui/styled-engine" "^5.16.6"
    "@mui/types" "^7.2.15"
    "@mui/utils" "^5.16.6"
    clsx "^2.1.0"
    csstype "^3.1.3"
    prop-types "^15.8.1"

"@mui/types@^7.2.15":
  version "7.2.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/types/-/@mui/types-7.2.15.tgz#dadd232fe9a70be0d526630675dff3b110f30b53"
  integrity sha1-2t0jL+mnC+DVJmMGdd/zsRDzC1M=

"@mui/utils@^5.16.6":
  version "5.16.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/utils/-/@mui/utils-5.16.6.tgz#905875bbc58d3dcc24531c3314a6807aba22a711"
  integrity sha1-kFh1u8WNPcwkUxwzFKaAeroipxE=
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@mui/types" "^7.2.15"
    "@types/prop-types" "^15.7.12"
    clsx "^2.1.1"
    prop-types "^15.8.1"
    react-is "^18.3.1"

"@mui/x-data-grid@^7.13.0":
  version "7.14.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/x-data-grid/-/@mui/x-data-grid-7.14.0.tgz#7188a25867b065531f73c10a02ecc3e122b8eb04"
  integrity sha1-cYiiWGewZVMfc8EKAuzD4SK46wQ=
  dependencies:
    "@babel/runtime" "^7.25.0"
    "@mui/system" "^5.16.7"
    "@mui/utils" "^5.16.6"
    "@mui/x-internals" "7.14.0"
    clsx "^2.1.1"
    prop-types "^15.8.1"
    reselect "^4.1.8"

"@mui/x-internals@7.14.0":
  version "7.14.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/x-internals/-/@mui/x-internals-7.14.0.tgz#d02ae32cc72e4f558aee771cc37a8ade9c3c53d7"
  integrity sha1-0CrjLMcuT1WK7nccw3qK3pw8U9c=
  dependencies:
    "@babel/runtime" "^7.25.0"
    "@mui/utils" "^5.16.6"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@nodelib/fs.scandir/-/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@nodelib/fs.stat/-/@nodelib/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@nodelib/fs.walk/-/@nodelib/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pmmmwh/react-refresh-webpack-plugin@^0.5.3":
  version "0.5.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@pmmmwh/react-refresh-webpack-plugin/-/@pmmmwh/react-refresh-webpack-plugin-0.5.13.tgz#02338a92a92f541a5189b97e922caf3215221e49"
  integrity sha1-AjOKkqkvVBpRibl+kiyvMhUiHkk=
  dependencies:
    ansi-html-community "^0.0.8"
    core-js-pure "^3.23.3"
    error-stack-parser "^2.0.6"
    html-entities "^2.1.0"
    loader-utils "^2.0.4"
    schema-utils "^3.0.0"
    source-map "^0.7.3"

"@popperjs/core@^2.11.8":
  version "2.11.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@popperjs/core/-/@popperjs/core-2.11.8.tgz#6b79032e760a0899cd4204710beede972a3a185f"
  integrity sha1-a3kDLnYKCJnNQgRxC+7elyo6GF8=

"@remix-run/router@1.16.0":
  version "1.16.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@remix-run/router/-/@remix-run/router-1.16.0.tgz#0e10181e5fec1434eb071a9bc4bdaac843f16dcc"
  integrity sha1-DhAYHl/sFDTrBxqbxL2qyEPxbcw=

"@sinclair/typebox@^0.27.8":
  version "0.27.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@sinclair/typebox/-/@sinclair/typebox-0.27.8.tgz#6667fac16c436b5434a387a34dedb013198f6e6e"
  integrity sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=

"@svgr/babel-plugin-add-jsx-attribute@^5.4.0":
  version "5.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-add-jsx-attribute/-/@svgr/babel-plugin-add-jsx-attribute-5.4.0.tgz#81ef61947bb268eb9d50523446f9c638fb355906"
  integrity sha1-ge9hlHuyaOudUFI0RvnGOPs1WQY=

"@svgr/babel-plugin-remove-jsx-attribute@^5.4.0":
  version "5.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-remove-jsx-attribute/-/@svgr/babel-plugin-remove-jsx-attribute-5.4.0.tgz#6b2c770c95c874654fd5e1d5ef475b78a0a962ef"
  integrity sha1-ayx3DJXIdGVP1eHV70dbeKCpYu8=

"@svgr/babel-plugin-remove-jsx-empty-expression@^5.0.1":
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-remove-jsx-empty-expression/-/@svgr/babel-plugin-remove-jsx-empty-expression-5.0.1.tgz#25621a8915ed7ad70da6cea3d0a6dbc2ea933efd"
  integrity sha1-JWIaiRXtetcNps6j0KbbwuqTPv0=

"@svgr/babel-plugin-replace-jsx-attribute-value@^5.0.1":
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-replace-jsx-attribute-value/-/@svgr/babel-plugin-replace-jsx-attribute-value-5.0.1.tgz#0b221fc57f9fcd10e91fe219e2cd0dd03145a897"
  integrity sha1-CyIfxX+fzRDpH+IZ4s0N0DFFqJc=

"@svgr/babel-plugin-svg-dynamic-title@^5.4.0":
  version "5.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-svg-dynamic-title/-/@svgr/babel-plugin-svg-dynamic-title-5.4.0.tgz#139b546dd0c3186b6e5db4fefc26cb0baea729d7"
  integrity sha1-E5tUbdDDGGtuXbT+/CbLC66nKdc=

"@svgr/babel-plugin-svg-em-dimensions@^5.4.0":
  version "5.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-svg-em-dimensions/-/@svgr/babel-plugin-svg-em-dimensions-5.4.0.tgz#6543f69526632a133ce5cabab965deeaea2234a0"
  integrity sha1-ZUP2lSZjKhM85cq6uWXe6uoiNKA=

"@svgr/babel-plugin-transform-react-native-svg@^5.4.0":
  version "5.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-transform-react-native-svg/-/@svgr/babel-plugin-transform-react-native-svg-5.4.0.tgz#00bf9a7a73f1cad3948cdab1f8dfb774750f8c80"
  integrity sha1-AL+aenPxytOUjNqx+N+3dHUPjIA=

"@svgr/babel-plugin-transform-svg-component@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-transform-svg-component/-/@svgr/babel-plugin-transform-svg-component-5.5.0.tgz#583a5e2a193e214da2f3afeb0b9e8d3250126b4a"
  integrity sha1-WDpeKhk+IU2i86/rC56NMlASa0o=

"@svgr/babel-preset@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-preset/-/@svgr/babel-preset-5.5.0.tgz#8af54f3e0a8add7b1e2b0fcd5a882c55393df327"
  integrity sha1-ivVPPgqK3XseKw/NWogsVTk98yc=
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "^5.0.1"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "^5.0.1"
    "@svgr/babel-plugin-svg-dynamic-title" "^5.4.0"
    "@svgr/babel-plugin-svg-em-dimensions" "^5.4.0"
    "@svgr/babel-plugin-transform-react-native-svg" "^5.4.0"
    "@svgr/babel-plugin-transform-svg-component" "^5.5.0"

"@svgr/core@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/core/-/@svgr/core-5.5.0.tgz#82e826b8715d71083120fe8f2492ec7d7874a579"
  integrity sha1-gugmuHFdcQgxIP6PJJLsfXh0pXk=
  dependencies:
    "@svgr/plugin-jsx" "^5.5.0"
    camelcase "^6.2.0"
    cosmiconfig "^7.0.0"

"@svgr/hast-util-to-babel-ast@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/hast-util-to-babel-ast/-/@svgr/hast-util-to-babel-ast-5.5.0.tgz#5ee52a9c2533f73e63f8f22b779f93cd432a5461"
  integrity sha1-XuUqnCUz9z5j+PIrd5+TzUMqVGE=
  dependencies:
    "@babel/types" "^7.12.6"

"@svgr/plugin-jsx@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/plugin-jsx/-/@svgr/plugin-jsx-5.5.0.tgz#1aa8cd798a1db7173ac043466d7b52236b369000"
  integrity sha1-GqjNeYodtxc6wENGbXtSI2s2kAA=
  dependencies:
    "@babel/core" "^7.12.3"
    "@svgr/babel-preset" "^5.5.0"
    "@svgr/hast-util-to-babel-ast" "^5.5.0"
    svg-parser "^2.0.2"

"@svgr/plugin-svgo@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/plugin-svgo/-/@svgr/plugin-svgo-5.5.0.tgz#02da55d85320549324e201c7b2e53bf431fcc246"
  integrity sha1-AtpV2FMgVJMk4gHHsuU79DH8wkY=
  dependencies:
    cosmiconfig "^7.0.0"
    deepmerge "^4.2.2"
    svgo "^1.2.2"

"@svgr/webpack@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/webpack/-/@svgr/webpack-5.5.0.tgz#aae858ee579f5fa8ce6c3166ef56c6a1b381b640"
  integrity sha1-quhY7lefX6jObDFm71bGobOBtkA=
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/plugin-transform-react-constant-elements" "^7.12.1"
    "@babel/preset-env" "^7.12.1"
    "@babel/preset-react" "^7.12.5"
    "@svgr/core" "^5.5.0"
    "@svgr/plugin-jsx" "^5.5.0"
    "@svgr/plugin-svgo" "^5.5.0"
    loader-utils "^2.0.0"

"@trysound/sax@0.2.0":
  version "0.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@trysound/sax/-/@trysound/sax-0.2.0.tgz#cccaab758af56761eb7bf37af6f03f326dd798ad"
  integrity sha1-zMqrdYr1Z2Hre/N69vA/Mm3XmK0=

"@types/body-parser@*":
  version "1.19.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/body-parser/-/@types/body-parser-1.19.5.tgz#04ce9a3b677dc8bd681a17da1ab9835dc9d3ede4"
  integrity sha1-BM6aO2d9yL1oGhfaGrmDXcnT7eQ=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@^3.5.9":
  version "3.5.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/bonjour/-/@types/bonjour-3.5.13.tgz#adf90ce1a105e81dd1f9c61fdc5afda1bfb92956"
  integrity sha1-rfkM4aEF6B3R+cYf3Fr9ob+5KVY=
  dependencies:
    "@types/node" "*"

"@types/connect-history-api-fallback@^1.3.5":
  version "1.5.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/connect-history-api-fallback/-/@types/connect-history-api-fallback-1.5.4.tgz#7de71645a103056b48ac3ce07b3520b819c1d5b3"
  integrity sha1-fecWRaEDBWtIrDzgezUguBnB1bM=
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/connect/-/@types/connect-3.4.38.tgz#5ba7f3bc4fbbdeaff8dded952e5ff2cc53f8d858"
  integrity sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=
  dependencies:
    "@types/node" "*"

"@types/eslint-scope@^3.7.3":
  version "3.7.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/eslint-scope/-/@types/eslint-scope-3.7.7.tgz#3108bd5f18b0cdb277c867b3dd449c9ed7079ac5"
  integrity sha1-MQi9XxiwzbJ3yGez3UScntcHmsU=
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "8.56.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/eslint/-/@types/eslint-8.56.10.tgz#eb2370a73bf04a901eeba8f22595c7ee0f7eb58d"
  integrity sha1-6yNwpzvwSpAe66jyJZXH7g9+tY0=
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.5":
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/estree/-/@types/estree-1.0.5.tgz#a6ce3e556e00fd9895dd872dd172ad0d4bd687f4"
  integrity sha1-ps4+VW4A/ZiV3Yct0XKtDUvWh/Q=

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.33":
  version "4.19.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/express-serve-static-core/-/@types/express-serve-static-core-4.19.0.tgz#3ae8ab3767d98d0b682cda063c3339e1e86ccfaa"
  integrity sha1-OuirN2fZjQtoLNoGPDM54ehsz6o=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*", "@types/express@^4.17.13":
  version "4.17.21"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/express/-/@types/express-4.17.21.tgz#c26d4a151e60efe0084b23dc3369ebc631ed192d"
  integrity sha1-wm1KFR5g7+AISyPcM2nrxjHtGS0=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/hoist-non-react-statics@^3.3.1":
  version "3.3.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/hoist-non-react-statics/-/@types/hoist-non-react-statics-3.3.5.tgz#dab7867ef789d87e2b4b0003c9d65c49cc44a494"
  integrity sha1-2reGfveJ2H4rSwADydZcScxEpJQ=
  dependencies:
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"

"@types/html-minifier-terser@^6.0.0":
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/html-minifier-terser/-/@types/html-minifier-terser-6.1.0.tgz#4fc33a00c1d0c16987b1a20cf92d20614c55ac35"
  integrity sha1-T8M6AMHQwWmHsaIM+S0gYUxVrDU=

"@types/http-errors@*":
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/http-errors/-/@types/http-errors-2.0.4.tgz#7eb47726c391b7345a6ec35ad7f4de469cf5ba4f"
  integrity sha1-frR3JsORtzRabsNa1/TeRpz1uk8=

"@types/http-proxy@^1.17.8":
  version "1.17.14"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/http-proxy/-/@types/http-proxy-1.17.14.tgz#57f8ccaa1c1c3780644f8a94f9c6b5000b5e2eec"
  integrity sha1-V/jMqhwcN4BkT4qU+ca1AAteLuw=
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0":
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/istanbul-lib-coverage/-/@types/istanbul-lib-coverage-2.0.6.tgz#7739c232a1fee9b4d3ce8985f314c0c6d33549d7"
  integrity sha1-dznCMqH+6bTTzomF8xTAxtM1Sdc=

"@types/istanbul-lib-report@*":
  version "3.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/istanbul-lib-report/-/@types/istanbul-lib-report-3.0.3.tgz#53047614ae72e19fc0401d872de3ae2b4ce350bf"
  integrity sha1-UwR2FK5y4Z/AQB2HLeOuK0zjUL8=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/istanbul-reports/-/@types/istanbul-reports-3.0.4.tgz#0f03e3d2f670fbdac586e34b433783070cc16f54"
  integrity sha1-DwPj0vZw+9rFhuNLQzeDBwzBb1Q=
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.4", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/json-schema/-/@types/json-schema-7.0.15.tgz#596a1747233694d50f6ad8a7869fcb6f56cf5841"
  integrity sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=

"@types/mime@^1":
  version "1.3.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/mime/-/@types/mime-1.3.5.tgz#1ef302e01cf7d2b5a0fa526790c9123bf1d06690"
  integrity sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=

"@types/node-forge@^1.3.0":
  version "1.3.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/node-forge/-/@types/node-forge-1.3.11.tgz#0972ea538ddb0f4d9c2fa0ec5db5724773a604da"
  integrity sha1-CXLqU43bD02cL6DsXbVyR3OmBNo=
  dependencies:
    "@types/node" "*"

"@types/node@*":
  version "20.12.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/node/-/@types/node-20.12.10.tgz#8f0c3f12b0f075eee1fe20c1afb417e9765bef76"
  integrity sha1-jww/ErDwde7h/iDBr7QX6XZb73Y=
  dependencies:
    undici-types "~5.26.4"

"@types/node@^16.18.12":
  version "16.18.97"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/node/-/@types/node-16.18.97.tgz#d7926a8030f0d714d555b4550c0cc7731495cfe5"
  integrity sha1-15JqgDDw1xTVVbRVDAzHcxSVz+U=

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/parse-json/-/@types/parse-json-4.0.2.tgz#5950e50960793055845e956c427fc2b0d70c5239"
  integrity sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk=

"@types/prop-types@*", "@types/prop-types@^15.7.12":
  version "15.7.12"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/prop-types/-/@types/prop-types-15.7.12.tgz#12bb1e2be27293c1406acb6af1c3f3a1481d98c6"
  integrity sha1-ErseK+Jyk8FAastq8cPzoUgdmMY=

"@types/q@^1.5.1":
  version "1.5.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/q/-/@types/q-1.5.8.tgz#95f6c6a08f2ad868ba230ead1d2d7f7be3db3837"
  integrity sha1-lfbGoI8q2Gi6Iw6tHS1/e+PbODc=

"@types/qs@*":
  version "6.9.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/qs/-/@types/qs-6.9.15.tgz#adde8a060ec9c305a82de1babc1056e73bd64dce"
  integrity sha1-rd6KBg7JwwWoLeG6vBBW5zvWTc4=

"@types/range-parser@*":
  version "1.2.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/range-parser/-/@types/range-parser-1.2.7.tgz#50ae4353eaaddc04044279812f52c8c65857dbcb"
  integrity sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=

"@types/react-dom@^18.0.11":
  version "18.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/react-dom/-/@types/react-dom-18.3.0.tgz#0cbc818755d87066ab6ca74fbedb2547d74a82b0"
  integrity sha1-DLyBh1XYcGarbKdPvtslR9dKgrA=
  dependencies:
    "@types/react" "*"

"@types/react-transition-group@^4.4.10":
  version "4.4.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/react-transition-group/-/@types/react-transition-group-4.4.10.tgz#6ee71127bdab1f18f11ad8fb3322c6da27c327ac"
  integrity sha1-bucRJ72rHxjxGtj7MyLG2ifDJ6w=
  dependencies:
    "@types/react" "*"

"@types/react@*", "@types/react@^18.0.28":
  version "18.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/react/-/@types/react-18.3.1.tgz#fed43985caa834a2084d002e4771e15dfcbdbe8e"
  integrity sha1-/tQ5hcqoNKIITQAuR3HhXfy9vo4=
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/retry@0.12.0":
  version "0.12.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/retry/-/@types/retry-0.12.0.tgz#2b35eccfcee7d38cd72ad99232fbd58bffb3c84d"
  integrity sha1-KzXsz87n04zXKtmSMvvVi/+zyE0=

"@types/send@*":
  version "0.17.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/send/-/@types/send-0.17.4.tgz#6619cd24e7270793702e4e6a4b958a9010cfc57a"
  integrity sha1-ZhnNJOcnB5NwLk5qS5WKkBDPxXo=
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-index@^1.9.1":
  version "1.9.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/serve-index/-/@types/serve-index-1.9.4.tgz#e6ae13d5053cb06ed36392110b4f9a49ac4ec898"
  integrity sha1-5q4T1QU8sG7TY5IRC0+aSaxOyJg=
  dependencies:
    "@types/express" "*"

"@types/serve-static@*", "@types/serve-static@^1.13.10":
  version "1.15.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/serve-static/-/@types/serve-static-1.15.7.tgz#22174bbd74fb97fe303109738e9b5c2f3064f714"
  integrity sha1-IhdLvXT7l/4wMQlzjptcLzBk9xQ=
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/sockjs@^0.3.33":
  version "0.3.36"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/sockjs/-/@types/sockjs-0.3.36.tgz#ce322cf07bcc119d4cbf7f88954f3a3bd0f67535"
  integrity sha1-zjIs8HvMEZ1Mv3+IlU86O9D2dTU=
  dependencies:
    "@types/node" "*"

"@types/uuid@^9.0.1":
  version "9.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/uuid/-/@types/uuid-9.0.8.tgz#7545ba4fc3c003d6c756f651f3bf163d8f0f29ba"
  integrity sha1-dUW6T8PAA9bHVvZR878WPY8PKbo=

"@types/ws@^8.5.5":
  version "8.5.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/ws/-/@types/ws-8.5.10.tgz#4acfb517970853fa6574a3a6886791d04a396787"
  integrity sha1-Ss+1F5cIU/pldKOmiGeR0Eo5Z4c=
  dependencies:
    "@types/node" "*"

"@types/yargs-parser@*":
  version "21.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/yargs-parser/-/@types/yargs-parser-21.0.3.tgz#815e30b786d2e8f0dcd85fd5bcf5e1a04d008f15"
  integrity sha1-gV4wt4bS6PDc2F/VvPXhoE0AjxU=

"@types/yargs@^17.0.8":
  version "17.0.32"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/yargs/-/@types/yargs-17.0.32.tgz#030774723a2f7faafebf645f4e5a48371dca6229"
  integrity sha1-Awd0cjovf6r+v2RfTlpINx3KYik=
  dependencies:
    "@types/yargs-parser" "*"

"@types/yup@^0.32.0":
  version "0.32.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/yup/-/@types/yup-0.32.0.tgz#2a19c3bbbb3aed1bd755204f83e800f9c95f249a"
  integrity sha1-KhnDu7s67RvXVSBPg+gA+clfJJo=
  dependencies:
    yup "*"

"@webassemblyjs/ast@1.12.1", "@webassemblyjs/ast@^1.12.1":
  version "1.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/ast/-/@webassemblyjs/ast-1.12.1.tgz#bb16a0e8b1914f979f45864c23819cc3e3f0d4bb"
  integrity sha1-uxag6LGRT5efRYZMI4Gcw+Pw1Ls=
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"

"@webassemblyjs/floating-point-hex-parser@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/floating-point-hex-parser/-/@webassemblyjs/floating-point-hex-parser-1.11.6.tgz#dacbcb95aff135c8260f77fa3b4c5fea600a6431"
  integrity sha1-2svLla/xNcgmD3f6O0xf6mAKZDE=

"@webassemblyjs/helper-api-error@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/helper-api-error/-/@webassemblyjs/helper-api-error-1.11.6.tgz#6132f68c4acd59dcd141c44b18cbebbd9f2fa768"
  integrity sha1-YTL2jErNWdzRQcRLGMvrvZ8vp2g=

"@webassemblyjs/helper-buffer@1.12.1":
  version "1.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/helper-buffer/-/@webassemblyjs/helper-buffer-1.12.1.tgz#6df20d272ea5439bf20ab3492b7fb70e9bfcb3f6"
  integrity sha1-bfINJy6lQ5vyCrNJK3+3Dpv8s/Y=

"@webassemblyjs/helper-numbers@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/helper-numbers/-/@webassemblyjs/helper-numbers-1.11.6.tgz#cbce5e7e0c1bd32cf4905ae444ef64cea919f1b5"
  integrity sha1-y85efgwb0yz0kFrkRO9kzqkZ8bU=
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.6"
    "@webassemblyjs/helper-api-error" "1.11.6"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/helper-wasm-bytecode/-/@webassemblyjs/helper-wasm-bytecode-1.11.6.tgz#bb2ebdb3b83aa26d9baad4c46d4315283acd51e9"
  integrity sha1-uy69s7g6om2bqtTEbUMVKDrNUek=

"@webassemblyjs/helper-wasm-section@1.12.1":
  version "1.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/helper-wasm-section/-/@webassemblyjs/helper-wasm-section-1.12.1.tgz#3da623233ae1a60409b509a52ade9bc22a37f7bf"
  integrity sha1-PaYjIzrhpgQJtQmlKt6bwio3978=
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-buffer" "1.12.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/wasm-gen" "1.12.1"

"@webassemblyjs/ieee754@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/ieee754/-/@webassemblyjs/ieee754-1.11.6.tgz#bb665c91d0b14fffceb0e38298c329af043c6e3a"
  integrity sha1-u2ZckdCxT//OsOOCmMMprwQ8bjo=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/leb128/-/@webassemblyjs/leb128-1.11.6.tgz#70e60e5e82f9ac81118bc25381a0b283893240d7"
  integrity sha1-cOYOXoL5rIERi8JTgaCyg4kyQNc=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/utf8/-/@webassemblyjs/utf8-1.11.6.tgz#90f8bc34c561595fe156603be7253cdbcd0fab5a"
  integrity sha1-kPi8NMVhWV/hVmA75yU8280Pq1o=

"@webassemblyjs/wasm-edit@^1.12.1":
  version "1.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/wasm-edit/-/@webassemblyjs/wasm-edit-1.12.1.tgz#9f9f3ff52a14c980939be0ef9d5df9ebc678ae3b"
  integrity sha1-n58/9SoUyYCTm+DvnV3568Z4rjs=
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-buffer" "1.12.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/helper-wasm-section" "1.12.1"
    "@webassemblyjs/wasm-gen" "1.12.1"
    "@webassemblyjs/wasm-opt" "1.12.1"
    "@webassemblyjs/wasm-parser" "1.12.1"
    "@webassemblyjs/wast-printer" "1.12.1"

"@webassemblyjs/wasm-gen@1.12.1":
  version "1.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/wasm-gen/-/@webassemblyjs/wasm-gen-1.12.1.tgz#a6520601da1b5700448273666a71ad0a45d78547"
  integrity sha1-plIGAdobVwBEgnNmanGtCkXXhUc=
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/ieee754" "1.11.6"
    "@webassemblyjs/leb128" "1.11.6"
    "@webassemblyjs/utf8" "1.11.6"

"@webassemblyjs/wasm-opt@1.12.1":
  version "1.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/wasm-opt/-/@webassemblyjs/wasm-opt-1.12.1.tgz#9e6e81475dfcfb62dab574ac2dda38226c232bc5"
  integrity sha1-nm6BR138+2LatXSsLdo4ImwjK8U=
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-buffer" "1.12.1"
    "@webassemblyjs/wasm-gen" "1.12.1"
    "@webassemblyjs/wasm-parser" "1.12.1"

"@webassemblyjs/wasm-parser@1.12.1", "@webassemblyjs/wasm-parser@^1.12.1":
  version "1.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/wasm-parser/-/@webassemblyjs/wasm-parser-1.12.1.tgz#c47acb90e6f083391e3fa61d113650eea1e95937"
  integrity sha1-xHrLkObwgzkeP6YdETZQ7qHpWTc=
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-api-error" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/ieee754" "1.11.6"
    "@webassemblyjs/leb128" "1.11.6"
    "@webassemblyjs/utf8" "1.11.6"

"@webassemblyjs/wast-printer@1.12.1":
  version "1.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/wast-printer/-/@webassemblyjs/wast-printer-1.12.1.tgz#bcecf661d7d1abdaf989d8341a4833e33e2b31ac"
  integrity sha1-vOz2YdfRq9r5idg0Gkgz4z4rMaw=
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@xtuc/long" "4.2.2"

"@webpack-cli/configtest@^2.1.1":
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webpack-cli/configtest/-/@webpack-cli/configtest-2.1.1.tgz#3b2f852e91dac6e3b85fb2a314fb8bef46d94646"
  integrity sha1-Oy+FLpHaxuO4X7KjFPuL70bZRkY=

"@webpack-cli/info@^2.0.2":
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webpack-cli/info/-/@webpack-cli/info-2.0.2.tgz#cc3fbf22efeb88ff62310cf885c5b09f44ae0fdd"
  integrity sha1-zD+/Iu/riP9iMQz4hcWwn0SuD90=

"@webpack-cli/serve@^2.0.5":
  version "2.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webpack-cli/serve/-/@webpack-cli/serve-2.0.5.tgz#325db42395cd49fe6c14057f9a900e427df8810e"
  integrity sha1-Ml20I5XNSf5sFAV/mpAOQn34gQ4=

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@xtuc/ieee754/-/@xtuc/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@xtuc/long/-/@xtuc/long-4.2.2.tgz#d291c6a4e97989b5c61d9acf396ae4fe133a718d"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.8:
  version "1.3.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/accepts/-/accepts-1.3.8.tgz#0bf0be125b67014adcb0b0921e62db7bffe16b2e"
  integrity sha1-C/C+EltnAUrcsLCSHmLbe//hay4=
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-import-assertions@^1.9.0:
  version "1.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/acorn-import-assertions/-/acorn-import-assertions-1.9.0.tgz#507276249d684797c84e0734ef84860334cfb1ac"
  integrity sha1-UHJ2JJ1oR5fITgc074SGAzTPsaw=

acorn@^8.7.1, acorn@^8.8.2:
  version "8.11.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/acorn/-/acorn-8.11.3.tgz#71e0b14e13a4ec160724b38fb7b0f233b1b81d7a"
  integrity sha1-ceCxThOk7BYHJLOPt7DyM7G4HXo=

address@^1.0.1, address@^1.1.2:
  version "1.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/address/-/address-1.2.2.tgz#2b5248dac5485a6390532c6a517fda2e3faac89e"
  integrity sha1-K1JI2sVIWmOQUyxqUX/aLj+qyJ4=

adjust-sourcemap-loader@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/adjust-sourcemap-loader/-/adjust-sourcemap-loader-4.0.0.tgz#fc4a0fd080f7d10471f30a7320f25560ade28c99"
  integrity sha1-/EoP0ID30QRx8wpzIPJVYK3ijJk=
  dependencies:
    loader-utils "^2.0.0"
    regex-parser "^2.2.11"

ajv-formats@^2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ajv-formats/-/ajv-formats-2.1.1.tgz#6e669400659eb74973bbf2e33327180a0996b520"
  integrity sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.4.1, ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ajv-keywords/-/ajv-keywords-3.5.2.tgz#31f29da5ab6e00d1c2d329acf7b5929614d5014d"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv-keywords@^5.0.0, ajv-keywords@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ajv-keywords/-/ajv-keywords-5.1.0.tgz#69d4d385a4733cdbeab44964a1170a88f87f0e16"
  integrity sha1-adTThaRzPNvqtElkoRcKiPh/DhY=
  dependencies:
    fast-deep-equal "^3.1.3"

ajv@^6.12.2, ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ajv/-/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0, ajv@^8.12.0, ajv@^8.9.0:
  version "8.13.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ajv/-/ajv-8.13.0.tgz#a3939eaec9fb80d217ddf0c3376948c023f28c91"
  integrity sha1-o5Oersn7gNIX3fDDN2lIwCPyjJE=
  dependencies:
    fast-deep-equal "^3.1.3"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.4.1"

ansi-html-community@^0.0.8:
  version "0.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ansi-html-community/-/ansi-html-community-0.0.8.tgz#69fbc4d6ccbe383f9736934ae34c3f8290f1bf41"
  integrity sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ansi-regex/-/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ansi-styles/-/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ansi-styles/-/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/anymatch/-/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

array-buffer-byte-length@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array-buffer-byte-length/-/array-buffer-byte-length-1.0.1.tgz#1e5583ec16763540a27ae52eed99ff899223568f"
  integrity sha1-HlWD7BZ2NUCieuUu7Zn/iZIjVo8=
  dependencies:
    call-bind "^1.0.5"
    is-array-buffer "^3.0.4"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array-flatten/-/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array-union/-/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array.prototype.reduce@^1.0.6:
  version "1.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array.prototype.reduce/-/array.prototype.reduce-1.0.7.tgz#6aadc2f995af29cb887eb866d981dc85ab6f7dc7"
  integrity sha1-aq3C+ZWvKcuIfrhm2YHchatvfcc=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-array-method-boxes-properly "^1.0.0"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    is-string "^1.0.7"

arraybuffer.prototype.slice@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.3.tgz#097972f4255e41bc3425e37dc3f6421cf9aefde6"
  integrity sha1-CXly9CVeQbw0JeN9w/ZCHPmu/eY=
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    es-abstract "^1.22.3"
    es-errors "^1.2.1"
    get-intrinsic "^1.2.3"
    is-array-buffer "^3.0.4"
    is-shared-array-buffer "^1.0.2"

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/at-least-node/-/at-least-node-1.0.0.tgz#602cd4b46e844ad4effc92a8011a3c46e0238dc2"
  integrity sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=

autoprefixer@^10.4.13:
  version "10.4.19"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/autoprefixer/-/autoprefixer-10.4.19.tgz#ad25a856e82ee9d7898c59583c1afeb3fa65f89f"
  integrity sha1-rSWoVugu6deJjFlYPBr+s/pl+J8=
  dependencies:
    browserslist "^4.23.0"
    caniuse-lite "^1.0.30001599"
    fraction.js "^4.3.7"
    normalize-range "^0.1.2"
    picocolors "^1.0.0"
    postcss-value-parser "^4.2.0"

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz#a5cc375d6a03c2efc87a553f3e0b1522def14846"
  integrity sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=
  dependencies:
    possible-typed-array-names "^1.0.0"

babel-loader@^8.2.3:
  version "8.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-loader/-/babel-loader-8.3.0.tgz#124936e841ba4fe8176786d6ff28add1f134d6a8"
  integrity sha1-Ekk26EG6T+gXZ4bW/yit0fE01qg=
  dependencies:
    find-cache-dir "^3.3.1"
    loader-utils "^2.0.0"
    make-dir "^3.1.0"
    schema-utils "^2.6.5"

babel-plugin-macros@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz#9ef6dc74deb934b4db344dc973ee851d148c50c1"
  integrity sha1-nvbcdN65NLTbNE3Jc+6FHRSMUME=
  dependencies:
    "@babel/runtime" "^7.12.5"
    cosmiconfig "^7.0.0"
    resolve "^1.19.0"

babel-plugin-polyfill-corejs2@^0.4.10:
  version "0.4.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.11.tgz#30320dfe3ffe1a336c15afdcdafd6fd615b25e33"
  integrity sha1-MDIN/j/+GjNsFa/c2v1v1hWyXjM=
  dependencies:
    "@babel/compat-data" "^7.22.6"
    "@babel/helper-define-polyfill-provider" "^0.6.2"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.10.1, babel-plugin-polyfill-corejs3@^0.10.4:
  version "0.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.10.4.tgz#789ac82405ad664c20476d0233b485281deb9c77"
  integrity sha1-eJrIJAWtZkwgR20CM7SFKB3rnHc=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.1"
    core-js-compat "^3.36.1"

babel-plugin-polyfill-regenerator@^0.6.1:
  version "0.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.2.tgz#addc47e240edd1da1058ebda03021f382bba785e"
  integrity sha1-rdxH4kDt0doQWOvaAwIfOCu6eF4=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.2"

babel-plugin-transform-react-remove-prop-types@^0.4.24:
  version "0.4.24"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-plugin-transform-react-remove-prop-types/-/babel-plugin-transform-react-remove-prop-types-0.4.24.tgz#f2edaf9b4c6a5fbe5c1d678bfb531078c1555f3a"
  integrity sha1-8u2vm0xqX75cHWeL+1MQeMFVXzo=

babel-preset-react-app@^10.0.1:
  version "10.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-preset-react-app/-/babel-preset-react-app-10.0.1.tgz#ed6005a20a24f2c88521809fa9aea99903751584"
  integrity sha1-7WAFogok8siFIYCfqa6pmQN1FYQ=
  dependencies:
    "@babel/core" "^7.16.0"
    "@babel/plugin-proposal-class-properties" "^7.16.0"
    "@babel/plugin-proposal-decorators" "^7.16.4"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.16.0"
    "@babel/plugin-proposal-numeric-separator" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.0"
    "@babel/plugin-proposal-private-methods" "^7.16.0"
    "@babel/plugin-transform-flow-strip-types" "^7.16.0"
    "@babel/plugin-transform-react-display-name" "^7.16.0"
    "@babel/plugin-transform-runtime" "^7.16.4"
    "@babel/preset-env" "^7.16.4"
    "@babel/preset-react" "^7.16.0"
    "@babel/preset-typescript" "^7.16.0"
    "@babel/runtime" "^7.16.3"
    babel-plugin-macros "^3.1.0"
    babel-plugin-transform-react-remove-prop-types "^0.4.24"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/balanced-match/-/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

batch@0.6.1:
  version "0.6.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/batch/-/batch-0.6.1.tgz#dc34314f4e679318093fc760272525f94bf25c16"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/big.js/-/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/binary-extensions/-/binary-extensions-2.3.0.tgz#f6e14a97858d327252200242d4ccfe522c445522"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

body-parser@1.20.2:
  version "1.20.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/body-parser/-/body-parser-1.20.2.tgz#6feb0e21c4724d06de7ff38da36dad4f57a747fd"
  integrity sha1-b+sOIcRyTQbef/ONo22tT1enR/0=
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

bonjour-service@^1.0.11:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/bonjour-service/-/bonjour-service-1.2.1.tgz#eb41b3085183df3321da1264719fbada12478d02"
  integrity sha1-60GzCFGD3zMh2hJkcZ+62hJHjQI=
  dependencies:
    fast-deep-equal "^3.1.3"
    multicast-dns "^7.2.5"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/boolbase/-/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/brace-expansion/-/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/braces/-/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

browserslist@^4.0.0, browserslist@^4.18.1, browserslist@^4.21.10, browserslist@^4.21.4, browserslist@^4.22.2, browserslist@^4.23.0:
  version "4.23.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/browserslist/-/browserslist-4.23.0.tgz#8f3acc2bbe73af7213399430890f86c63a5674ab"
  integrity sha1-jzrMK75zr3ITOZQwiQ+GxjpWdKs=
  dependencies:
    caniuse-lite "^1.0.30001587"
    electron-to-chromium "^1.4.668"
    node-releases "^2.0.14"
    update-browserslist-db "^1.0.13"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/buffer-from/-/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

bytes@3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/bytes/-/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.2:
  version "3.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/bytes/-/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

call-bind@^1.0.2, call-bind@^1.0.5, call-bind@^1.0.6, call-bind@^1.0.7:
  version "1.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/call-bind/-/call-bind-1.0.7.tgz#06016599c40c56498c18769d2730be242b6fa3b9"
  integrity sha1-BgFlmcQMVkmMGHadJzC+JCtvo7k=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/callsites/-/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@^4.1.2:
  version "4.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/camel-case/-/camel-case-4.1.2.tgz#9728072a954f805228225a6deea6b38461e1bd5a"
  integrity sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

camelcase@^6.2.0:
  version "6.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/camelcase/-/camelcase-6.3.0.tgz#5685b95eb209ac9c0c177467778c9c84df58ba9a"
  integrity sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/caniuse-api/-/caniuse-api-3.0.0.tgz#5e4d90e2274961d46291997df599e3ed008ee4c0"
  integrity sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30001587, caniuse-lite@^1.0.30001599:
  version "1.0.30001616"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/caniuse-lite/-/caniuse-lite-1.0.30001616.tgz#4342712750d35f71ebba9fcac65e2cf8870013c3"
  integrity sha1-Q0JxJ1DTX3Hrup/Kxl4s+IcAE8M=

chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/chalk/-/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/chalk/-/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

"chokidar@>=3.0.0 <4.0.0", chokidar@^3.4.2, chokidar@^3.5.3:
  version "3.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/chokidar/-/chokidar-3.6.0.tgz#197c6cc669ef2a8dc5e7b4d97ee4e092c3eb0d5b"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chrome-trace-event@^1.0.2:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz#1015eced4741e15d06664a957dbbf50d041e26ac"
  integrity sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw=

ci-info@^3.2.0:
  version "3.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ci-info/-/ci-info-3.9.0.tgz#4279a62028a7b1f262f3473fc9605f5e218c59b4"
  integrity sha1-QnmmICinsfJi80c/yWBfXiGMWbQ=

clean-css@^5.2.2:
  version "5.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/clean-css/-/clean-css-5.3.3.tgz#b330653cd3bd6b75009cc25c714cae7b93351ccd"
  integrity sha1-szBlPNO9a3UAnMJccUyue5M1HM0=
  dependencies:
    source-map "~0.6.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/clone-deep/-/clone-deep-4.0.1.tgz#c19fd9bdbbf85942b4fd979c84dcf7d5f07c2387"
  integrity sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clsx@^2.1.0, clsx@^2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/clsx/-/clsx-2.1.1.tgz#eed397c9fd8bd882bfb18deab7102049a2f32999"
  integrity sha1-7tOXyf2L2IK/sY3qtxAgSaLzKZk=

coa@^2.0.2:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/coa/-/coa-2.0.2.tgz#43f6c21151b4ef2bf57187db0d73de229e3e7ec3"
  integrity sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=
  dependencies:
    "@types/q" "^1.5.1"
    chalk "^2.4.1"
    q "^1.1.2"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/color-convert/-/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/color-convert/-/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/color-name/-/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/color-name/-/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colord@^2.9.1:
  version "2.9.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/colord/-/colord-2.9.3.tgz#4f8ce919de456f1d5c1c368c307fe20f3e59fb43"
  integrity sha1-T4zpGd5Fbx1cHDaMMH/iDz5Z+0M=

colorette@^2.0.10, colorette@^2.0.14:
  version "2.0.20"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/colorette/-/colorette-2.0.20.tgz#9eb793e6833067f7235902fcd3b09917a000a95a"
  integrity sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=

commander@^10.0.1:
  version "10.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/commander/-/commander-10.0.1.tgz#881ee46b4f77d1c1dccc5823433aa39b022cbe06"
  integrity sha1-iB7ka0930cHczFgjQzqjmwIsvgY=

commander@^2.20.0:
  version "2.20.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/commander/-/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^7.2.0:
  version "7.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/commander/-/commander-7.2.0.tgz#a36cb57d0b501ce108e4d20559a150a391d97ab7"
  integrity sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc=

commander@^8.3.0:
  version "8.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/commander/-/commander-8.3.0.tgz#4837ea1b2da67b9c616a67afbb0fafee567bca66"
  integrity sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/compressible/-/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/compression/-/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

connect-history-api-fallback@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/connect-history-api-fallback/-/connect-history-api-fallback-2.0.0.tgz#647264845251a0daf25b97ce87834cace0f5f1c8"
  integrity sha1-ZHJkhFJRoNryW5fOh4NMrOD18cg=

content-disposition@0.5.4:
  version "0.5.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/content-disposition/-/content-disposition-0.5.4.tgz#8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe"
  integrity sha1-i4K076yCUSoCuwsdzsnSxejrW/4=
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4, content-type@~1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/content-type/-/content-type-1.0.5.tgz#8b773162656d1d1086784c8f23a54ce6d73d7918"
  integrity sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=

convert-source-map@^1.5.0, convert-source-map@^1.7.0:
  version "1.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/convert-source-map/-/convert-source-map-1.9.0.tgz#7faae62353fb4213366d0ca98358d22e8368b05f"
  integrity sha1-f6rmI1P7QhM2bQypg1jSLoNosF8=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/convert-source-map/-/convert-source-map-2.0.0.tgz#4b560f649fc4e918dd0ab75cf4961e8bc882d82a"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cookie-signature/-/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.6.0:
  version "0.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cookie/-/cookie-0.6.0.tgz#2798b04b071b0ecbff0dbb62a505a8efa4e19051"
  integrity sha1-J5iwSwcbDsv/DbtipQWo76ThkFE=

core-js-compat@^3.31.0, core-js-compat@^3.36.1:
  version "3.37.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/core-js-compat/-/core-js-compat-3.37.0.tgz#d9570e544163779bb4dff1031c7972f44918dc73"
  integrity sha1-2VcOVEFjd5u03/EDHHly9EkY3HM=
  dependencies:
    browserslist "^4.23.0"

core-js-pure@^3.23.3:
  version "3.37.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/core-js-pure/-/core-js-pure-3.37.0.tgz#ce99fb4a7cec023fdbbe5b5bd1f06bbcba83316e"
  integrity sha1-zpn7SnzsAj/bvltb0fBrvLqDMW4=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/core-util-is/-/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cosmiconfig@^6.0.0:
  version "6.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cosmiconfig/-/cosmiconfig-6.0.0.tgz#da4fee853c52f6b1e6935f41c1a2fc50bd4a9982"
  integrity sha1-2k/uhTxS9rHmk19BwaL8UL1KmYI=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.1.0"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.7.2"

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cosmiconfig/-/cosmiconfig-7.1.0.tgz#1443b9afa596b670082ea46cbd8f6a62b84635f6"
  integrity sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cross-spawn/-/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-blank-pseudo@^3.0.3:
  version "3.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-blank-pseudo/-/css-blank-pseudo-3.0.3.tgz#36523b01c12a25d812df343a32c322d2a2324561"
  integrity sha1-NlI7AcEqJdgS3zQ6MsMi0qIyRWE=
  dependencies:
    postcss-selector-parser "^6.0.9"

css-declaration-sorter@^6.3.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-declaration-sorter/-/css-declaration-sorter-6.4.1.tgz#28beac7c20bad7f1775be3a7129d7eae409a3a71"
  integrity sha1-KL6sfCC61/F3W+OnEp1+rkCaOnE=

css-has-pseudo@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-has-pseudo/-/css-has-pseudo-3.0.4.tgz#57f6be91ca242d5c9020ee3e51bbb5b89fc7af73"
  integrity sha1-V/a+kcokLVyQIO4+Ubu1uJ/Hr3M=
  dependencies:
    postcss-selector-parser "^6.0.9"

css-loader@^6.7.3:
  version "6.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-loader/-/css-loader-6.11.0.tgz#33bae3bf6363d0a7c2cf9031c96c744ff54d85ba"
  integrity sha1-M7rjv2Nj0KfCz5AxyWx0T/VNhbo=
  dependencies:
    icss-utils "^5.1.0"
    postcss "^8.4.33"
    postcss-modules-extract-imports "^3.1.0"
    postcss-modules-local-by-default "^4.0.5"
    postcss-modules-scope "^3.2.0"
    postcss-modules-values "^4.0.0"
    postcss-value-parser "^4.2.0"
    semver "^7.5.4"

css-minimizer-webpack-plugin@^4.2.2:
  version "4.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-minimizer-webpack-plugin/-/css-minimizer-webpack-plugin-4.2.2.tgz#79f6199eb5adf1ff7ba57f105e3752d15211eb35"
  integrity sha1-efYZnrWt8f97pX8QXjdS0VIR6zU=
  dependencies:
    cssnano "^5.1.8"
    jest-worker "^29.1.2"
    postcss "^8.4.17"
    schema-utils "^4.0.0"
    serialize-javascript "^6.0.0"
    source-map "^0.6.1"

css-prefers-color-scheme@^6.0.3:
  version "6.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-prefers-color-scheme/-/css-prefers-color-scheme-6.0.3.tgz#ca8a22e5992c10a5b9d315155e7caee625903349"
  integrity sha1-yooi5ZksEKW50xUVXnyu5iWQM0k=

css-select-base-adapter@^0.1.1:
  version "0.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz#3b2ff4972cc362ab88561507a95408a1432135d7"
  integrity sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc=

css-select@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-select/-/css-select-2.1.0.tgz#6a34653356635934a81baca68d0255432105dbef"
  integrity sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8=
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-select@^4.1.3:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-select/-/css-select-4.3.0.tgz#db7129b2846662fd8628cfc496abb2b59e41529b"
  integrity sha1-23EpsoRmYv2GKM/ElquytZ5BUps=
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.0.1"
    domhandler "^4.3.1"
    domutils "^2.8.0"
    nth-check "^2.0.1"

css-tree@1.0.0-alpha.37:
  version "1.0.0-alpha.37"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-tree/-/css-tree-1.0.0-alpha.37.tgz#98bebd62c4c1d9f960ec340cf9f7522e30709a22"
  integrity sha1-mL69YsTB2flg7DQM+fdSLjBwmiI=
  dependencies:
    mdn-data "2.0.4"
    source-map "^0.6.1"

css-tree@^1.1.2, css-tree@^1.1.3:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-tree/-/css-tree-1.1.3.tgz#eb4870fb6fd7707327ec95c2ff2ab09b5e8db91d"
  integrity sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-what@^3.2.1:
  version "3.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-what/-/css-what-3.4.2.tgz#ea7026fcb01777edbde52124e21f327e7ae950e4"
  integrity sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ=

css-what@^6.0.1:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-what/-/css-what-6.1.0.tgz#fb5effcf76f1ddea2c81bdfaa4de44e79bac70f4"
  integrity sha1-+17/z3bx3eosgb36pN5E55uscPQ=

cssdb@^7.1.0:
  version "7.11.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cssdb/-/cssdb-7.11.2.tgz#127a2f5b946ee653361a5af5333ea85a39df5ae5"
  integrity sha1-EnovW5Ru5lM2Glr1Mz6oWjnfWuU=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cssesc/-/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssnano-preset-default@^5.2.14:
  version "5.2.14"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cssnano-preset-default/-/cssnano-preset-default-5.2.14.tgz#309def4f7b7e16d71ab2438052093330d9ab45d8"
  integrity sha1-MJ3vT3t+FtcaskOAUgkzMNmrRdg=
  dependencies:
    css-declaration-sorter "^6.3.1"
    cssnano-utils "^3.1.0"
    postcss-calc "^8.2.3"
    postcss-colormin "^5.3.1"
    postcss-convert-values "^5.1.3"
    postcss-discard-comments "^5.1.2"
    postcss-discard-duplicates "^5.1.0"
    postcss-discard-empty "^5.1.1"
    postcss-discard-overridden "^5.1.0"
    postcss-merge-longhand "^5.1.7"
    postcss-merge-rules "^5.1.4"
    postcss-minify-font-values "^5.1.0"
    postcss-minify-gradients "^5.1.1"
    postcss-minify-params "^5.1.4"
    postcss-minify-selectors "^5.2.1"
    postcss-normalize-charset "^5.1.0"
    postcss-normalize-display-values "^5.1.0"
    postcss-normalize-positions "^5.1.1"
    postcss-normalize-repeat-style "^5.1.1"
    postcss-normalize-string "^5.1.0"
    postcss-normalize-timing-functions "^5.1.0"
    postcss-normalize-unicode "^5.1.1"
    postcss-normalize-url "^5.1.0"
    postcss-normalize-whitespace "^5.1.1"
    postcss-ordered-values "^5.1.3"
    postcss-reduce-initial "^5.1.2"
    postcss-reduce-transforms "^5.1.0"
    postcss-svgo "^5.1.0"
    postcss-unique-selectors "^5.1.1"

cssnano-utils@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cssnano-utils/-/cssnano-utils-3.1.0.tgz#95684d08c91511edfc70d2636338ca37ef3a6861"
  integrity sha1-lWhNCMkVEe38cNJjYzjKN+86aGE=

cssnano@^5.1.8:
  version "5.1.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cssnano/-/cssnano-5.1.15.tgz#ded66b5480d5127fcb44dac12ea5a983755136bf"
  integrity sha1-3tZrVIDVEn/LRNrBLqWpg3VRNr8=
  dependencies:
    cssnano-preset-default "^5.2.14"
    lilconfig "^2.0.3"
    yaml "^1.10.2"

csso@^4.0.2, csso@^4.2.0:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/csso/-/csso-4.2.0.tgz#ea3a561346e8dc9f546d6febedd50187cf389529"
  integrity sha1-6jpWE0bo3J9UbW/r7dUBh884lSk=
  dependencies:
    css-tree "^1.1.2"

csstype@^3.0.2, csstype@^3.1.3:
  version "3.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/csstype/-/csstype-3.1.3.tgz#d80ff294d114fb0e6ac500fbf85b60137d7eff81"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

data-view-buffer@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/data-view-buffer/-/data-view-buffer-1.0.1.tgz#8ea6326efec17a2e42620696e671d7d5a8bc66b2"
  integrity sha1-jqYybv7Bei5CYgaW5nHX1ai8ZrI=
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

data-view-byte-length@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/data-view-byte-length/-/data-view-byte-length-1.0.1.tgz#90721ca95ff280677eb793749fce1011347669e2"
  integrity sha1-kHIcqV/ygGd+t5N0n84QETR2aeI=
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

data-view-byte-offset@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/data-view-byte-offset/-/data-view-byte-offset-1.0.0.tgz#5e0bbfb4828ed2d1b9b400cd8a7d119bca0ff18a"
  integrity sha1-Xgu/tIKO0tG5tADNin0Rm8oP8Yo=
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

date-fns@^2.8.0:
  version "2.30.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/date-fns/-/date-fns-2.30.0.tgz#f367e644839ff57894ec6ac480de40cae4b0f4d0"
  integrity sha1-82fmRIOf9XiU7GrEgN5AyuSw9NA=
  dependencies:
    "@babel/runtime" "^7.21.0"

dayjs@^1.11.7:
  version "1.11.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dayjs/-/dayjs-1.11.11.tgz#dfe0e9d54c5f8b68ccf8ca5f72ac603e7e5ed59e"
  integrity sha1-3+Dp1Uxfi2jM+MpfcqxgPn5e1Z4=

debug@2.6.9, debug@^2.6.0:
  version "2.6.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/debug/-/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^4.1.0, debug@^4.1.1, debug@^4.3.1:
  version "4.3.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/debug/-/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=
  dependencies:
    ms "2.1.2"

deepmerge@^2.1.1:
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/deepmerge/-/deepmerge-2.2.1.tgz#5d3ff22a01c00f645405a2fbc17d0778a1801170"
  integrity sha1-XT/yKgHAD2RUBaL7wX0HeKGAEXA=

deepmerge@^4.2.2:
  version "4.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/deepmerge/-/deepmerge-4.3.1.tgz#44b5f2147cd3b00d4b56137685966f26fd25dd4a"
  integrity sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=

default-gateway@^6.0.3:
  version "6.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/default-gateway/-/default-gateway-6.0.3.tgz#819494c888053bdb743edbf343d6cdf7f2943a71"
  integrity sha1-gZSUyIgFO9t0PtvzQ9bN9/KUOnE=
  dependencies:
    execa "^5.0.0"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/define-data-property/-/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz#3f7ae421129bcaaac9bc74905c98a0009ec9ee7f"
  integrity sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=

define-properties@^1.1.3, define-properties@^1.2.0, define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/define-properties/-/define-properties-1.2.1.tgz#10781cc616eb951a80a034bafcaa7377f6af2b6c"
  integrity sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

depd@2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/depd/-/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/depd/-/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

destroy@1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/destroy/-/destroy-1.2.0.tgz#4803735509ad8be552934c67df614f94e66fa015"
  integrity sha1-SANzVQmti+VSk0xn32FPlOZvoBU=

detect-node@^2.0.4:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/detect-node/-/detect-node-2.1.0.tgz#c9c70775a49c3d03bc2c06d9a73be550f978f8b1"
  integrity sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=

detect-port-alt@^1.1.6:
  version "1.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/detect-port-alt/-/detect-port-alt-1.1.6.tgz#24707deabe932d4a3cf621302027c2b266568275"
  integrity sha1-JHB96r6TLUo89iEwICfCsmZWgnU=
  dependencies:
    address "^1.0.1"
    debug "^2.6.0"

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dir-glob/-/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dns-packet@^5.2.2:
  version "5.6.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dns-packet/-/dns-packet-5.6.1.tgz#ae888ad425a9d1478a0674256ab866de1012cf2f"
  integrity sha1-roiK1CWp0UeKBnQlarhm3hASzy8=
  dependencies:
    "@leichtgewicht/ip-codec" "^2.0.1"

dom-converter@^0.2.0:
  version "0.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dom-converter/-/dom-converter-0.2.0.tgz#6721a9daee2e293682955b6afe416771627bb768"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-helpers@^5.0.1:
  version "5.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dom-helpers/-/dom-helpers-5.2.1.tgz#d9400536b2bf8225ad98fe052e029451ac40e902"
  integrity sha1-2UAFNrK/giWtmP4FLgKUUaxA6QI=
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

dom-serializer@0:
  version "0.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dom-serializer/-/dom-serializer-0.2.2.tgz#1afb81f533717175d478655debc5e332d9f9bb51"
  integrity sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

dom-serializer@^1.0.1:
  version "1.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dom-serializer/-/dom-serializer-1.4.1.tgz#de5d41b1aea290215dc45a6dae8adcf1d32e2d30"
  integrity sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

domelementtype@1:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/domelementtype/-/domelementtype-1.3.1.tgz#d048c44b37b0d10a7f2a3d5fee3f4333d790481f"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domelementtype@^2.0.1, domelementtype@^2.2.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/domelementtype/-/domelementtype-2.3.0.tgz#5c45e8e869952626331d7aab326d01daf65d589d"
  integrity sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=

domhandler@^4.0.0, domhandler@^4.2.0, domhandler@^4.3.1:
  version "4.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/domhandler/-/domhandler-4.3.1.tgz#8d792033416f59d68bc03a5aa7b018c1ca89279c"
  integrity sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=
  dependencies:
    domelementtype "^2.2.0"

domutils@^1.7.0:
  version "1.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/domutils/-/domutils-1.7.0.tgz#56ea341e834e06e6748af7a1cb25da67ea9f8c2a"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^2.5.2, domutils@^2.8.0:
  version "2.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/domutils/-/domutils-2.8.0.tgz#4437def5db6e2d1f5d6ee859bd95ca7d02048135"
  integrity sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dot-case/-/dot-case-3.0.4.tgz#9b2b670d00a431667a8a75ba29cd1b98809ce751"
  integrity sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dotenv-expand@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dotenv-expand/-/dotenv-expand-5.1.0.tgz#3fbaf020bfd794884072ea26b1e9791d45a629f0"
  integrity sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=

dotenv@^10.0.0:
  version "10.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dotenv/-/dotenv-10.0.0.tgz#3d4227b8fb95f81096cdd2b66653fb2c7085ba81"
  integrity sha1-PUInuPuV+BCWzdK2ZlP7LHCFuoE=

duplexer@^0.1.2:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/duplexer/-/duplexer-0.1.2.tgz#3abe43aef3835f8ae077d136ddce0f276b0400e6"
  integrity sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ee-first/-/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-to-chromium@^1.4.668:
  version "1.4.757"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/electron-to-chromium/-/electron-to-chromium-1.4.757.tgz#45f7c9341b538f8c4b9ca8af9692e0ed1a776a44"
  integrity sha1-RffJNBtTj4xLnKivlpLg7Rp3akQ=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/emojis-list/-/emojis-list-3.0.0.tgz#5570662046ad29e2e916e71aae260abdff4f6a78"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/encodeurl/-/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

enhanced-resolve@^5.16.0:
  version "5.16.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/enhanced-resolve/-/enhanced-resolve-5.16.0.tgz#65ec88778083056cb32487faa9aef82ed0864787"
  integrity sha1-ZeyId4CDBWyzJIf6qa74LtCGR4c=
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

entities@^2.0.0:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/entities/-/entities-2.2.0.tgz#098dc90ebb83d8dffa089d55256b351d34c4da55"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

envinfo@^7.7.3:
  version "7.13.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/envinfo/-/envinfo-7.13.0.tgz#81fbb81e5da35d74e814941aeab7c325a606fb31"
  integrity sha1-gfu4Hl2jXXToFJQa6rfDJaYG+zE=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/error-ex/-/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/error-stack-parser/-/error-stack-parser-2.1.4.tgz#229cb01cdbfa84440bfa91876285b94680188286"
  integrity sha1-IpywHNv6hEQL+pGHYoW5RoAYgoY=
  dependencies:
    stackframe "^1.3.4"

es-abstract@^1.17.2, es-abstract@^1.22.1, es-abstract@^1.22.3, es-abstract@^1.23.0, es-abstract@^1.23.2:
  version "1.23.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-abstract/-/es-abstract-1.23.3.tgz#8f0c5a35cd215312573c5a27c87dfd6c881a0aa0"
  integrity sha1-jwxaNc0hUxJXPFonyH39bIgaCqA=
  dependencies:
    array-buffer-byte-length "^1.0.1"
    arraybuffer.prototype.slice "^1.0.3"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    data-view-buffer "^1.0.1"
    data-view-byte-length "^1.0.1"
    data-view-byte-offset "^1.0.0"
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-set-tostringtag "^2.0.3"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.6"
    get-intrinsic "^1.2.4"
    get-symbol-description "^1.0.2"
    globalthis "^1.0.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"
    has-proto "^1.0.3"
    has-symbols "^1.0.3"
    hasown "^2.0.2"
    internal-slot "^1.0.7"
    is-array-buffer "^3.0.4"
    is-callable "^1.2.7"
    is-data-view "^1.0.1"
    is-negative-zero "^2.0.3"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.3"
    is-string "^1.0.7"
    is-typed-array "^1.1.13"
    is-weakref "^1.0.2"
    object-inspect "^1.13.1"
    object-keys "^1.1.1"
    object.assign "^4.1.5"
    regexp.prototype.flags "^1.5.2"
    safe-array-concat "^1.1.2"
    safe-regex-test "^1.0.3"
    string.prototype.trim "^1.2.9"
    string.prototype.trimend "^1.0.8"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.2"
    typed-array-byte-length "^1.0.1"
    typed-array-byte-offset "^1.0.2"
    typed-array-length "^1.0.6"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.15"

es-array-method-boxes-properly@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-array-method-boxes-properly/-/es-array-method-boxes-properly-1.0.0.tgz#873f3e84418de4ee19c5be752990b2e44718d09e"
  integrity sha1-hz8+hEGN5O4Zxb51KZCy5EcY0J4=

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-define-property/-/es-define-property-1.0.0.tgz#c7faefbdff8b2696cf5f46921edfb77cc4ba3845"
  integrity sha1-x/rvvf+LJpbPX0aSHt+3fMS6OEU=
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.2.1, es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-errors/-/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-module-lexer@^1.2.1:
  version "1.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-module-lexer/-/es-module-lexer-1.5.2.tgz#00b423304f2500ac59359cc9b6844951f372d497"
  integrity sha1-ALQjME8lAKxZNZzJtoRJUfNy1Jc=

es-object-atoms@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-object-atoms/-/es-object-atoms-1.0.0.tgz#ddb55cd47ac2e240701260bc2a8e31ecb643d941"
  integrity sha1-3bVc1HrC4kBwEmC8Ko4x7LZD2UE=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.3:
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-set-tostringtag/-/es-set-tostringtag-2.0.3.tgz#8bb60f0a440c2e4281962428438d58545af39777"
  integrity sha1-i7YPCkQMLkKBliQoQ41YVFrzl3c=
  dependencies:
    get-intrinsic "^1.2.4"
    has-tostringtag "^1.0.2"
    hasown "^2.0.1"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-to-primitive/-/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.2:
  version "3.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/escalade/-/escalade-3.1.2.tgz#54076e9ab29ea5bf3d8f1ed62acffbb88272df27"
  integrity sha1-VAdumrKepb89jx7WKs/7uIJy3yc=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/escape-html/-/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-scope@5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-scope/-/eslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/esprima/-/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/esrecurse/-/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/estraverse/-/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/estraverse/-/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/esutils/-/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/etag/-/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eventemitter3/-/eventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.2.0:
  version "3.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/events/-/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

execa@^5.0.0:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/execa/-/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

express@^4.17.3:
  version "4.19.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/express/-/express-4.19.2.tgz#e25437827a3aa7f2a827bc8171bbbb664a356465"
  integrity sha1-4lQ3gno6p/KoJ7yBcbu7Zko1ZGU=
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.2"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.6.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.11.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz#****************************************"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-glob@^3.2.9:
  version "3.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fast-glob/-/fast-glob-3.3.2.tgz#a904501e57cfdd2ffcded45e99a54fef55e46129"
  integrity sha1-qQRQHlfP3S/83tRemaVP71XkYSk=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fastest-levenshtein@^1.0.12:
  version "1.0.16"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz#210e61b6ff181de91ea9b3d1b84fdedd47e034e5"
  integrity sha1-IQ5htv8YHekeqbPRuE/e3UfgNOU=

fastq@^1.6.0:
  version "1.17.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fastq/-/fastq-1.17.1.tgz#2a523f07a4e7b1e81a42b91b8bf2254107753b47"
  integrity sha1-KlI/B6TnsegaQrkbi/IlQQd1O0c=
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3:
  version "0.11.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/faye-websocket/-/faye-websocket-0.11.4.tgz#7f0d9275cfdd86a1c963dc8b65fcc451edcbb1da"
  integrity sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=
  dependencies:
    websocket-driver ">=0.5.1"

file-loader@^6.2.0:
  version "6.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/file-loader/-/file-loader-6.2.0.tgz#baef7cf8e1840df325e4390b4484879480eebe4d"
  integrity sha1-uu98+OGEDfMl5DkLRISHlIDuvk0=
  dependencies:
    loader-utils "^2.0.0"
    schema-utils "^3.0.0"

filesize@^8.0.6:
  version "8.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/filesize/-/filesize-8.0.7.tgz#695e70d80f4e47012c132d57a059e80c6b580bd8"
  integrity sha1-aV5w2A9ORwEsEy1XoFnoDGtYC9g=

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fill-range/-/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/finalhandler/-/finalhandler-1.2.0.tgz#7d23fe5731b207b4640e4fcd00aec1f9207a7b32"
  integrity sha1-fSP+VzGyB7RkDk/NAK7B+SB6ezI=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-cache-dir@^3.3.1:
  version "3.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/find-cache-dir/-/find-cache-dir-3.3.2.tgz#b30c5b6eff0730731aea9bbd9dbecbd80256d64b"
  integrity sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-root@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/find-root/-/find-root-1.1.0.tgz#abcfc8ba76f708c42a97b3d685b7e9450bfb9ce4"
  integrity sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ=

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/find-up/-/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/find-up/-/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/find-up/-/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat@^5.0.2:
  version "5.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/flat/-/flat-5.0.2.tgz#8ca6fe332069ffa9d324c327198c598259ceb241"
  integrity sha1-jKb+MyBp/6nTJMMnGYxZglnOskE=

follow-redirects@^1.0.0:
  version "1.15.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/follow-redirects/-/follow-redirects-1.15.6.tgz#7f815c0cda4249c74ff09e95ef97c23b5fd0399b"
  integrity sha1-f4FcDNpCScdP8J6V75fCO1/QOZs=

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/for-each/-/for-each-0.3.3.tgz#69b447e88a0a5d32c3e7084f3f1710034b21376e"
  integrity sha1-abRH6IoKXTLD5whPPxcQA0shN24=
  dependencies:
    is-callable "^1.1.3"

fork-ts-checker-webpack-plugin@^6.5.0:
  version "6.5.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-6.5.3.tgz#eda2eff6e22476a2688d10661688c47f611b37f3"
  integrity sha1-7aLv9uIkdqJojRBmFojEf2EbN/M=
  dependencies:
    "@babel/code-frame" "^7.8.3"
    "@types/json-schema" "^7.0.5"
    chalk "^4.1.0"
    chokidar "^3.4.2"
    cosmiconfig "^6.0.0"
    deepmerge "^4.2.2"
    fs-extra "^9.0.0"
    glob "^7.1.6"
    memfs "^3.1.2"
    minimatch "^3.0.4"
    schema-utils "2.7.0"
    semver "^7.3.2"
    tapable "^1.0.0"

formik@^2.2.9:
  version "2.4.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/formik/-/formik-2.4.6.tgz#4da75ca80f1a827ab35b08fd98d5a76e928c9686"
  integrity sha1-TadcqA8agnqzWwj9mNWnbpKMloY=
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.1"
    deepmerge "^2.1.1"
    hoist-non-react-statics "^3.3.0"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    react-fast-compare "^2.0.1"
    tiny-warning "^1.0.2"
    tslib "^2.0.0"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/forwarded/-/forwarded-0.2.0.tgz#2269936428aad4c15c7ebe9779a84bf0b2a81811"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fraction.js@^4.3.7:
  version "4.3.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fraction.js/-/fraction.js-4.3.7.tgz#06ca0085157e42fda7f9e726e79fefc4068840f7"
  integrity sha1-BsoAhRV+Qv2n+ecm55/vxAaIQPc=

fresh@0.5.2:
  version "0.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fresh/-/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-extra@^9.0.0:
  version "9.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fs-extra/-/fs-extra-9.1.0.tgz#5954460c764a8da2094ba3554bf839e6b9a7c86d"
  integrity sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-monkey@^1.0.4:
  version "1.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fs-monkey/-/fs-monkey-1.0.6.tgz#8ead082953e88d992cf3ff844faa907b26756da2"
  integrity sha1-jq0IKVPojZks8/+ET6qQeyZ1baI=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.3.2:
  version "2.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/function-bind/-/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

function.prototype.name@^1.1.6:
  version "1.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/function.prototype.name/-/function.prototype.name-1.1.6.tgz#cdf315b7d90ee77a4c6ee216c3c3362da07533fd"
  integrity sha1-zfMVt9kO53pMbuIWw8M2LaB1M/0=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    functions-have-names "^1.2.3"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/functions-have-names/-/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/gensync/-/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-intrinsic@^1.1.3, get-intrinsic@^1.2.1, get-intrinsic@^1.2.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/get-intrinsic/-/get-intrinsic-1.2.4.tgz#e385f5a4b5227d449c3eabbad05494ef0abbeadd"
  integrity sha1-44X1pLUifUScPqu60FSU7wq76t0=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/get-stream/-/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-symbol-description@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/get-symbol-description/-/get-symbol-description-1.0.2.tgz#533744d5aa20aca4e079c8e5daf7fd44202821f5"
  integrity sha1-UzdE1aogrKTgecjl2vf9RCAoIfU=
  dependencies:
    call-bind "^1.0.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/glob-parent/-/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz#c75297087c851b9a578bd217dd59a92f59fe546e"
  integrity sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=

glob@^7.1.3, glob@^7.1.6:
  version "7.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/glob/-/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/global-modules/-/global-modules-2.0.0.tgz#997605ad2345f27f51539bea26574421215c7780"
  integrity sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/global-prefix/-/global-prefix-3.0.0.tgz#fc85f73064df69f50421f47f883fe5b913ba9b97"
  integrity sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/globals/-/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globalthis@^1.0.3:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/globalthis/-/globalthis-1.0.4.tgz#7430ed3a975d97bfb59bcce41f5cabbafa651236"
  integrity sha1-dDDtOpddl7+1m8zkH1yruvplEjY=
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

globby@^11.0.4:
  version "11.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/globby/-/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/gopd/-/gopd-1.0.1.tgz#29ff76de69dac7489b7c0918a5788e56477c332c"
  integrity sha1-Kf923mnax0ibfAkYpXiOVkd8Myw=
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.11, graceful-fs@^4.2.4, graceful-fs@^4.2.6, graceful-fs@^4.2.9:
  version "4.2.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/graceful-fs/-/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

gzip-size@^6.0.0:
  version "6.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/gzip-size/-/gzip-size-6.0.0.tgz#065367fd50c239c0671cbcbad5be3e2eeb10e462"
  integrity sha1-BlNn/VDCOcBnHLy61b4+LusQ5GI=
  dependencies:
    duplexer "^0.1.2"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/handle-thing/-/handle-thing-2.0.1.tgz#857f79ce359580c340d43081cc648970d0bb234e"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-bigints/-/has-bigints-1.0.2.tgz#0871bd3e3d51626f6ca0966668ba35d5602d6eaa"
  integrity sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-flag/-/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-flag/-/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1, has-proto@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-proto/-/has-proto-1.0.3.tgz#b31ddfe9b0e6e9914536a6ab286426d0214f77fd"
  integrity sha1-sx3f6bDm6ZFFNqarKGQm0CFPd/0=

has-symbols@^1.0.1, has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-symbols/-/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-tostringtag/-/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.0, hasown@^2.0.1, hasown@^2.0.2:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/hasown/-/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

he@^1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/he/-/he-1.2.0.tgz#84ae65fa7eafb165fddb61566ae14baf05664f0f"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

highcharts-react-official@3.2.1:
  version "3.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/highcharts-react-official/-/highcharts-react-official-3.2.1.tgz#4b62a7af2969bdebde6b338d36f6b9bf1f1029bc"
  integrity sha1-S2KnrylpveveazONNva5vx8QKbw=

highcharts@11.4.1:
  version "11.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/highcharts/-/highcharts-11.4.1.tgz#e40e97c4ea61a10389da75bff0cdb8f83bdb57e1"
  integrity sha1-5A6XxOphoQOJ2nW/8M24+DvbV+E=

hoist-non-react-statics@^3.3.0, hoist-non-react-statics@^3.3.1:
  version "3.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz#ece0acaf71d62c2969c2ec59feff42a4b1a85b45"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/hpack.js/-/hpack.js-2.1.6.tgz#87774c0949e513f42e84575b3c45681fade2a0b2"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-entities@^2.1.0, html-entities@^2.3.2:
  version "2.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/html-entities/-/html-entities-2.5.2.tgz#201a3cf95d3a15be7099521620d19dfb4f65359f"
  integrity sha1-IBo8+V06Fb5wmVIWINGd+09lNZ8=

html-minifier-terser@^6.0.2:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz#bfc818934cc07918f6b3669f5774ecdfd48f32ab"
  integrity sha1-v8gYk0zAeRj2s2afV3Ts39SPMqs=
  dependencies:
    camel-case "^4.1.2"
    clean-css "^5.2.2"
    commander "^8.3.0"
    he "^1.2.0"
    param-case "^3.0.4"
    relateurl "^0.2.7"
    terser "^5.10.0"

html-webpack-plugin@^5.5.0:
  version "5.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/html-webpack-plugin/-/html-webpack-plugin-5.6.0.tgz#50a8fa6709245608cb00e811eacecb8e0d7b7ea0"
  integrity sha1-UKj6ZwkkVgjLAOgR6s7Ljg17fqA=
  dependencies:
    "@types/html-minifier-terser" "^6.0.0"
    html-minifier-terser "^6.0.2"
    lodash "^4.17.21"
    pretty-error "^4.0.0"
    tapable "^2.0.0"

htmlparser2@^6.1.0:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/htmlparser2/-/htmlparser2-6.1.0.tgz#c4d762b6c3371a05dbe65e94ae43a9f845fb8fb7"
  integrity sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/http-deceiver/-/http-deceiver-1.2.7.tgz#fa7168944ab9a519d337cb0bec7284dc3e723d87"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/http-errors/-/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/http-errors/-/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-parser-js@>=0.5.1:
  version "0.5.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/http-parser-js/-/http-parser-js-0.5.8.tgz#af23090d9ac4e24573de6f6aecc9d84a48bf20e3"
  integrity sha1-ryMJDZrE4kVz3m9q7MnYSki/IOM=

http-proxy-middleware@^2.0.3:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/http-proxy-middleware/-/http-proxy-middleware-2.0.6.tgz#e1a4dd6979572c7ab5a4e4b55095d1f32a74963f"
  integrity sha1-4aTdaXlXLHq1pOS1UJXR8yp0lj8=
  dependencies:
    "@types/http-proxy" "^1.17.8"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy@^1.18.1:
  version "1.18.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/http-proxy/-/http-proxy-1.18.1.tgz#401541f0534884bbf95260334e72f88ee3976549"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/human-signals/-/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/iconv-lite/-/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-utils@^5.0.0, icss-utils@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/icss-utils/-/icss-utils-5.1.0.tgz#c6be6858abd013d768e98366ae47e25d5887b1ae"
  integrity sha1-xr5oWKvQE9do6YNmrkfiXViHsa4=

ignore@^5.2.0:
  version "5.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ignore/-/ignore-5.3.1.tgz#5073e554cd42c5b33b394375f538b8593e34d4ef"
  integrity sha1-UHPlVM1CxbM7OUN19Ti4WT401O8=

immer@^9.0.7:
  version "9.0.21"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/immer/-/immer-9.0.21.tgz#1e025ea31a40f24fb064f1fef23e931496330176"
  integrity sha1-HgJeoxpA8k+wZPH+8j6TFJYzAXY=

immutable@^4.0.0:
  version "4.3.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/immutable/-/immutable-4.3.5.tgz#f8b436e66d59f99760dc577f5c99a4fd2a5cc5a0"
  integrity sha1-+LQ25m1Z+Zdg3Fd/XJmk/SpcxaA=

import-fresh@^3.1.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/import-fresh/-/import-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/import-local/-/import-local-3.1.0.tgz#b4479df8a5fd44f6cdce24070675676063c95cb4"
  integrity sha1-tEed+KX9RPbNziQHBnVnYGPJXLQ=
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/inherits/-/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.5:
  version "1.3.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ini/-/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

internal-slot@^1.0.7:
  version "1.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/internal-slot/-/internal-slot-1.0.7.tgz#c06dcca3ed874249881007b0a5523b172a190802"
  integrity sha1-wG3Mo+2HQkmIEAewpVI7FyoZCAI=
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.0"
    side-channel "^1.0.4"

interpret@^3.1.1:
  version "3.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/interpret/-/interpret-3.1.1.tgz#5be0ceed67ca79c6c4bc5cf0d7ee843dcea110c4"
  integrity sha1-W+DO7WfKecbEvFzw1+6EPc6hEMQ=

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ipaddr.js/-/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

ipaddr.js@^2.0.1:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ipaddr.js/-/ipaddr.js-2.2.0.tgz#d33fa7bac284f4de7af949638c9d68157c6b92e8"
  integrity sha1-0z+nusKE9N56+UljjJ1oFXxrkug=

is-array-buffer@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-array-buffer/-/is-array-buffer-3.0.4.tgz#7a1f92b3d61edd2bc65d24f130530ea93d7fae98"
  integrity sha1-eh+Ss9Ye3SvGXSTxMFMOqT1/rpg=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.1"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-bigint/-/is-bigint-1.0.4.tgz#08147a1875bc2b32005d41ccd8291dffc6691df3"
  integrity sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-binary-path/-/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-boolean-object/-/is-boolean-object-1.1.2.tgz#5c6dc200246dd9321ae4b885a114bb1f75f63719"
  integrity sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-callable/-/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-core-module@^2.13.0:
  version "2.13.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-core-module/-/is-core-module-2.13.1.tgz#ad0d7532c6fea9da1ebdc82742d74525c6273384"
  integrity sha1-rQ11Msb+qdoevcgnQtdFJcYnM4Q=
  dependencies:
    hasown "^2.0.0"

is-data-view@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-data-view/-/is-data-view-1.0.1.tgz#4b4d3a511b70f3dc26d42c03ca9ca515d847759f"
  integrity sha1-S006URtw89wm1CwDypylFdhHdZ8=
  dependencies:
    is-typed-array "^1.1.13"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-date-object/-/is-date-object-1.0.5.tgz#0841d5536e724c25597bf6ea62e1bd38298df31f"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-docker/-/is-docker-2.2.1.tgz#33eeabe23cfe86f14bde4408a02c0cfb853acdaa"
  integrity sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-extglob/-/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-glob/-/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-negative-zero@^2.0.3:
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-negative-zero/-/is-negative-zero-2.0.3.tgz#ced903a027aca6381b777a5743069d7376a49747"
  integrity sha1-ztkDoCespjgbd3pXQwadc3akl0c=

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-number-object/-/is-number-object-1.0.7.tgz#59d50ada4c45251784e9904f5246c742f07a42fc"
  integrity sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw=
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-number/-/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-plain-obj/-/is-plain-obj-3.0.0.tgz#af6f2ea14ac5a646183a5bbdb5baabbc156ad9d7"
  integrity sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=

is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-plain-object/-/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-regex@^1.1.4:
  version "1.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-regex/-/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-root@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-root/-/is-root-2.1.0.tgz#809e18129cf1129644302a4f8544035d51984a9c"
  integrity sha1-gJ4YEpzxEpZEMCpPhUQDXVGYSpw=

is-shared-array-buffer@^1.0.2, is-shared-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-shared-array-buffer/-/is-shared-array-buffer-1.0.3.tgz#1237f1cba059cdb62431d378dcc37d9680181688"
  integrity sha1-Ejfxy6BZzbYkMdN43MN9loAYFog=
  dependencies:
    call-bind "^1.0.7"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-stream/-/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-string/-/is-string-1.0.7.tgz#0dd12bf2006f255bb58f695110eff7491eebc0fd"
  integrity sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-symbol/-/is-symbol-1.0.4.tgz#a6dac93b635b063ca6872236de88910a57af139c"
  integrity sha1-ptrJO2NbBjymhyI23oiRClevE5w=
  dependencies:
    has-symbols "^1.0.2"

is-typed-array@^1.1.13:
  version "1.1.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-typed-array/-/is-typed-array-1.1.13.tgz#d6c5ca56df62334959322d7d7dd1cca50debe229"
  integrity sha1-1sXKVt9iM0lZMi19fdHMpQ3r4ik=
  dependencies:
    which-typed-array "^1.1.14"

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-weakref/-/is-weakref-1.0.2.tgz#9529f383a9338205e89765e0392efc2f100f06f2"
  integrity sha1-lSnzg6kzggXol2XgOS78LxAPBvI=
  dependencies:
    call-bind "^1.0.2"

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-wsl/-/is-wsl-2.2.0.tgz#74a4c76e77ca9fd3f932f290c17ea326cd157271"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@^2.0.5:
  version "2.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/isarray/-/isarray-2.0.5.tgz#8af1e4c1221244cc62459faf38940d4e644a5723"
  integrity sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^3.0.1:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/isobject/-/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

jest-util@^29.7.0:
  version "29.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jest-util/-/jest-util-29.7.0.tgz#23c2b62bfb22be82b44de98055802ff3710fc0bc"
  integrity sha1-I8K2K/sivoK0TemAVYAv83EPwLw=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-worker@^27.4.5:
  version "27.5.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jest-worker/-/jest-worker-27.5.1.tgz#8d146f0900e8973b106b6f73cc1e9a8cb86f8db0"
  integrity sha1-jRRvCQDolzsQa29zzB6ajLhvjbA=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@^29.1.2:
  version "29.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jest-worker/-/jest-worker-29.7.0.tgz#acad073acbbaeb7262bd5389e1bcf43e10058d4a"
  integrity sha1-rK0HOsu663JivVOJ4bz0PhAFjUo=
  dependencies:
    "@types/node" "*"
    jest-util "^29.7.0"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/js-yaml/-/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jsesc/-/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jsesc/-/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json5@^2.1.2, json5@^2.2.3:
  version "2.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/json5/-/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jsonfile/-/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/kind-of/-/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/kleur/-/kleur-3.0.3.tgz#a79c9ecc86ee1ce3fa6206d1216c501f147fc07e"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

klona@^2.0.4, klona@^2.0.5:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/klona/-/klona-2.0.6.tgz#85bffbf819c03b2f53270412420a4555ef882e22"
  integrity sha1-hb/7+BnAOy9TJwQSQgpFVe+ILiI=

launch-editor@^2.6.0:
  version "2.6.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/launch-editor/-/launch-editor-2.6.1.tgz#f259c9ef95cbc9425620bbbd14b468fcdb4ffe3c"
  integrity sha1-8lnJ75XLyUJWILu9FLRo/NtP/jw=
  dependencies:
    picocolors "^1.0.0"
    shell-quote "^1.8.1"

lilconfig@^2.0.3:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lilconfig/-/lilconfig-2.1.0.tgz#78e23ac89ebb7e1bfbf25b18043de756548e7f52"
  integrity sha1-eOI6yJ67fhv78lsYBD3nVlSOf1I=

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lines-and-columns/-/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

loader-runner@^4.2.0:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/loader-runner/-/loader-runner-4.3.0.tgz#c1b4a163b99f614830353b16755e7149ac2314e1"
  integrity sha1-wbShY7mfYUgwNTsWdV5xSawjFOE=

loader-utils@^2.0.0, loader-utils@^2.0.4:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/loader-utils/-/loader-utils-2.0.4.tgz#8b5cb38b5c34a9a018ee1fc0e6a066d1dfcc528c"
  integrity sha1-i1yzi1w0qaAY7h/A5qBm0d/MUow=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

loader-utils@^3.2.0, loader-utils@^3.2.1:
  version "3.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/loader-utils/-/loader-utils-3.2.1.tgz#4fb104b599daafd82ef3e1a41fb9265f87e1f576"
  integrity sha1-T7EEtZnar9gu8+GkH7kmX4fh9XY=

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/locate-path/-/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/locate-path/-/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/locate-path/-/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.21:
  version "4.17.21"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lodash-es/-/lodash-es-4.17.21.tgz#43e626c46e6591b7750beb2b50117390c609e3ee"
  integrity sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lodash.debounce/-/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lodash.memoize/-/lodash.memoize-4.1.2.tgz#bcc6c49a42a2840ed997f323eada5ecd182e0bfe"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lodash.uniq/-/lodash.uniq-4.5.0.tgz#d0225373aeb652adc1bc82e4945339a842754773"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@^4.17.20, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lodash/-/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/loose-envify/-/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lower-case/-/lower-case-2.0.2.tgz#6fa237c63dbdc4a82ca0fd882e4722dc5e634e28"
  integrity sha1-b6I3xj29xKgsoP2ILkci3F5jTig=
  dependencies:
    tslib "^2.0.3"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lru-cache/-/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lru-cache/-/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

make-dir@^3.0.2, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/make-dir/-/make-dir-3.1.0.tgz#415e967046b3a7f1d185277d84aa58203726a13f"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mdn-data/-/mdn-data-2.0.14.tgz#7113fc4281917d63ce29b43446f701e68c25ba50"
  integrity sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=

mdn-data@2.0.4:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mdn-data/-/mdn-data-2.0.4.tgz#699b3c38ac6f1d728091a64650b65d388502fd5b"
  integrity sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs=

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/media-typer/-/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memfs@^3.1.2, memfs@^3.4.3:
  version "3.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/memfs/-/memfs-3.6.0.tgz#d7a2110f86f79dd950a8b6df6d57bc984aa185f6"
  integrity sha1-16IRD4b3ndlQqLbfbVe8mEqhhfY=
  dependencies:
    fs-monkey "^1.0.4"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/merge-descriptors/-/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/merge-stream/-/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/merge2/-/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@~1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/methods/-/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^4.0.2, micromatch@^4.0.4:
  version "4.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/micromatch/-/micromatch-4.0.5.tgz#bc8999a7cbbf77cdc89f132f6e467051b49090c6"
  integrity sha1-vImZp8u/d83InxMvbkZwUbSQkMY=
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.52.0, "mime-db@>= 1.43.0 < 2":
  version "1.52.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mime-db/-/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-types@^2.1.27, mime-types@^2.1.31, mime-types@~2.1.17, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mime-types/-/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mime/-/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mimic-fn/-/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mini-css-extract-plugin@^2.4.5:
  version "2.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mini-css-extract-plugin/-/mini-css-extract-plugin-2.9.0.tgz#c73a1327ccf466f69026ac22a8e8fd707b78a235"
  integrity sha1-xzoTJ8z0ZvaQJqwiqOj9cHt4ojU=
  dependencies:
    schema-utils "^4.0.0"
    tapable "^2.2.1"

minimalistic-assert@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimatch@^3.0.4, minimatch@^3.0.5, minimatch@^3.1.1:
  version "3.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/minimatch/-/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.2.6:
  version "1.2.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/minimist/-/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

mkdirp@~0.5.1:
  version "0.5.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mkdirp/-/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=
  dependencies:
    minimist "^1.2.6"

ms@2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ms/-/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@2.1.3:
  version "2.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

multicast-dns@^7.2.5:
  version "7.2.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/multicast-dns/-/multicast-dns-7.2.5.tgz#77eb46057f4d7adbd16d9290fa7299f6fa64cced"
  integrity sha1-d+tGBX9NetvRbZKQ+nKZ9vpkzO0=
  dependencies:
    dns-packet "^5.2.2"
    thunky "^1.0.2"

nanoid@^3.3.7:
  version "3.3.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/nanoid/-/nanoid-3.3.7.tgz#d0c301a691bc8d54efa0a2226ccf3fe2fd656bd8"
  integrity sha1-0MMBppG8jVTvoKIibM8/4v1la9g=

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/negotiator/-/negotiator-0.6.3.tgz#58e323a72fedc0d6f9cd4d31fe49f51479590ccd"
  integrity sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/neo-async/-/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

no-case@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/no-case/-/no-case-3.0.4.tgz#d361fd5c9800f558551a8369fc0dcd4662b6124d"
  integrity sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-forge@^1:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/node-forge/-/node-forge-1.3.1.tgz#be8da2af243b2417d5f646a770663a92b7e9ded3"
  integrity sha1-vo2iryQ7JBfV9kancGY6krfp3tM=

node-releases@^2.0.14:
  version "2.0.14"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/node-releases/-/node-releases-2.0.14.tgz#2ffb053bceb8b2be8495ece1ab6ce600c4461b0b"
  integrity sha1-L/sFO864sr6Elezhq2zmAMRGGws=

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/normalize-path/-/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/normalize-range/-/normalize-range-0.1.2.tgz#2d10c06bdfd312ea9777695a4d28439456b75942"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-url@^6.0.1:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/normalize-url/-/normalize-url-6.1.0.tgz#40d0885b535deffe3f3147bec877d05fe4c5668a"
  integrity sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo=

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/npm-run-path/-/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

nth-check@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/nth-check/-/nth-check-1.0.2.tgz#b2bd295c37e3dd58a3bf0700376663ba4d9cf05c"
  integrity sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=
  dependencies:
    boolbase "~1.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/nth-check/-/nth-check-2.1.1.tgz#c9eab428effce36cd6b92c924bdb000ef1f1ed1d"
  integrity sha1-yeq0KO/842zWuSySS9sADvHx7R0=
  dependencies:
    boolbase "^1.0.0"

object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-inspect@^1.13.1:
  version "1.13.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object-inspect/-/object-inspect-1.13.1.tgz#b96c6109324ccfef6b12216a956ca4dc2ff94bc2"
  integrity sha1-uWxhCTJMz+9rEiFqlWyk3C/5S8I=

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object-keys/-/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object.assign@^4.1.5:
  version "4.1.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object.assign/-/object.assign-4.1.5.tgz#3a833f9ab7fdb80fc9e8d2300c803d216d8fdbb0"
  integrity sha1-OoM/mrf9uA/J6NIwDIA9IW2P27A=
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.getownpropertydescriptors@^2.1.0:
  version "2.1.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.8.tgz#2f1fe0606ec1a7658154ccd4f728504f69667923"
  integrity sha1-Lx/gYG7Bp2WBVMzU9yhQT2lmeSM=
  dependencies:
    array.prototype.reduce "^1.0.6"
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"
    gopd "^1.0.1"
    safe-array-concat "^1.1.2"

object.values@^1.1.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object.values/-/object.values-1.2.0.tgz#65405a9d92cee68ac2d303002e0b8470a4d9ab1b"
  integrity sha1-ZUBanZLO5orC0wMALguEcKTZqxs=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/obuf/-/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

on-finished@2.4.1:
  version "2.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/on-finished/-/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/on-headers/-/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.2:
  version "5.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/onetime/-/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^8.0.9, open@^8.4.0:
  version "8.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/open/-/open-8.4.2.tgz#5b5ffe2a8f793dcd2aad73e550cb87b59cb084f9"
  integrity sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-limit/-/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-limit/-/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-locate/-/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-locate/-/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-locate/-/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

p-retry@^4.5.0:
  version "4.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-retry/-/p-retry-4.6.2.tgz#9baae7184057edd4e17231cee04264106e092a16"
  integrity sha1-m6rnGEBX7dThcjHO4EJkEG4JKhY=
  dependencies:
    "@types/retry" "0.12.0"
    retry "^0.13.1"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-try/-/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

param-case@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/param-case/-/param-case-3.0.4.tgz#7d17fe4aa12bde34d4a77d91acfb6219caad01c5"
  integrity sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/parent-module/-/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/parse-json/-/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/parseurl/-/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascal-case@^3.1.2:
  version "3.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/pascal-case/-/pascal-case-3.1.2.tgz#b48e0ef2b98e205e7c1dae747d0b1508237660eb"
  integrity sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-exists/-/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-exists/-/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-key/-/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-parse/-/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-to-regexp/-/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-type/-/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/picocolors/-/picocolors-1.0.0.tgz#cb5bdc74ff3f51892236eaf79d68bc44564ab81c"
  integrity sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.3, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/picomatch/-/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

pkg-dir@^4.1.0, pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/pkg-dir/-/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

pkg-up@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/pkg-up/-/pkg-up-3.1.0.tgz#100ec235cc150e4fd42519412596a28512a0def5"
  integrity sha1-EA7CNcwVDk/UJRlBJZaihRKg3vU=
  dependencies:
    find-up "^3.0.0"

possible-typed-array-names@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/possible-typed-array-names/-/possible-typed-array-names-1.0.0.tgz#89bb63c6fada2c3e90adc4a647beeeb39cc7bf8f"
  integrity sha1-ibtjxvraLD6QrcSmR77us5zHv48=

postcss-attribute-case-insensitive@^5.0.2:
  version "5.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-attribute-case-insensitive/-/postcss-attribute-case-insensitive-5.0.2.tgz#03d761b24afc04c09e757e92ff53716ae8ea2741"
  integrity sha1-A9dhskr8BMCedX6S/1NxaujqJ0E=
  dependencies:
    postcss-selector-parser "^6.0.10"

postcss-browser-comments@^4:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-browser-comments/-/postcss-browser-comments-4.0.0.tgz#bcfc86134df5807f5d3c0eefa191d42136b5e72a"
  integrity sha1-vPyGE031gH9dPA7voZHUITa15yo=

postcss-calc@^8.2.3:
  version "8.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-calc/-/postcss-calc-8.2.4.tgz#77b9c29bfcbe8a07ff6693dc87050828889739a5"
  integrity sha1-d7nCm/y+igf/ZpPchwUIKIiXOaU=
  dependencies:
    postcss-selector-parser "^6.0.9"
    postcss-value-parser "^4.2.0"

postcss-clamp@^4.1.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-clamp/-/postcss-clamp-4.1.0.tgz#7263e95abadd8c2ba1bd911b0b5a5c9c93e02363"
  integrity sha1-cmPpWrrdjCuhvZEbC1pcnJPgI2M=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-color-functional-notation@^4.2.4:
  version "4.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-color-functional-notation/-/postcss-color-functional-notation-4.2.4.tgz#21a909e8d7454d3612d1659e471ce4696f28caec"
  integrity sha1-IakJ6NdFTTYS0WWeRxzkaW8oyuw=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-color-hex-alpha@^8.0.4:
  version "8.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-color-hex-alpha/-/postcss-color-hex-alpha-8.0.4.tgz#c66e2980f2fbc1a63f5b079663340ce8b55f25a5"
  integrity sha1-xm4pgPL7waY/WweWYzQM6LVfJaU=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-color-rebeccapurple@^7.1.1:
  version "7.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-color-rebeccapurple/-/postcss-color-rebeccapurple-7.1.1.tgz#63fdab91d878ebc4dd4b7c02619a0c3d6a56ced0"
  integrity sha1-Y/2rkdh468TdS3wCYZoMPWpWztA=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-colormin@^5.3.1:
  version "5.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-colormin/-/postcss-colormin-5.3.1.tgz#86c27c26ed6ba00d96c79e08f3ffb418d1d1988f"
  integrity sha1-hsJ8Ju1roA2Wx54I8/+0GNHRmI8=
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"
    colord "^2.9.1"
    postcss-value-parser "^4.2.0"

postcss-convert-values@^5.1.3:
  version "5.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-convert-values/-/postcss-convert-values-5.1.3.tgz#04998bb9ba6b65aa31035d669a6af342c5f9d393"
  integrity sha1-BJmLubprZaoxA11mmmrzQsX505M=
  dependencies:
    browserslist "^4.21.4"
    postcss-value-parser "^4.2.0"

postcss-custom-media@^8.0.2:
  version "8.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-custom-media/-/postcss-custom-media-8.0.2.tgz#c8f9637edf45fef761b014c024cee013f80529ea"
  integrity sha1-yPljft9F/vdhsBTAJM7gE/gFKeo=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-custom-properties@^12.1.10:
  version "12.1.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-custom-properties/-/postcss-custom-properties-12.1.11.tgz#d14bb9b3989ac4d40aaa0e110b43be67ac7845cf"
  integrity sha1-0Uu5s5iaxNQKqg4RC0O+Z6x4Rc8=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-custom-selectors@^6.0.3:
  version "6.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-custom-selectors/-/postcss-custom-selectors-6.0.3.tgz#1ab4684d65f30fed175520f82d223db0337239d9"
  integrity sha1-GrRoTWXzD+0XVSD4LSI9sDNyOdk=
  dependencies:
    postcss-selector-parser "^6.0.4"

postcss-dir-pseudo-class@^6.0.5:
  version "6.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-dir-pseudo-class/-/postcss-dir-pseudo-class-6.0.5.tgz#2bf31de5de76added44e0a25ecf60ae9f7c7c26c"
  integrity sha1-K/Md5d52rd7UTgol7PYK6ffHwmw=
  dependencies:
    postcss-selector-parser "^6.0.10"

postcss-discard-comments@^5.1.2:
  version "5.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-discard-comments/-/postcss-discard-comments-5.1.2.tgz#8df5e81d2925af2780075840c1526f0660e53696"
  integrity sha1-jfXoHSklryeAB1hAwVJvBmDlNpY=

postcss-discard-duplicates@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-discard-duplicates/-/postcss-discard-duplicates-5.1.0.tgz#9eb4fe8456706a4eebd6d3b7b777d07bad03e848"
  integrity sha1-nrT+hFZwak7r1tO3t3fQe60D6Eg=

postcss-discard-empty@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-discard-empty/-/postcss-discard-empty-5.1.1.tgz#e57762343ff7f503fe53fca553d18d7f0c369c6c"
  integrity sha1-5XdiND/39QP+U/ylU9GNfww2nGw=

postcss-discard-overridden@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-discard-overridden/-/postcss-discard-overridden-5.1.0.tgz#7e8c5b53325747e9d90131bb88635282fb4a276e"
  integrity sha1-foxbUzJXR+nZATG7iGNSgvtKJ24=

postcss-double-position-gradients@^3.1.2:
  version "3.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-double-position-gradients/-/postcss-double-position-gradients-3.1.2.tgz#b96318fdb477be95997e86edd29c6e3557a49b91"
  integrity sha1-uWMY/bR3vpWZfobt0pxuNVekm5E=
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

postcss-env-function@^4.0.6:
  version "4.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-env-function/-/postcss-env-function-4.0.6.tgz#7b2d24c812f540ed6eda4c81f6090416722a8e7a"
  integrity sha1-ey0kyBL1QO1u2kyB9gkEFnIqjno=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-flexbugs-fixes@^5.0.2:
  version "5.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-flexbugs-fixes/-/postcss-flexbugs-fixes-5.0.2.tgz#2028e145313074fc9abe276cb7ca14e5401eb49d"
  integrity sha1-ICjhRTEwdPyavidst8oU5UAetJ0=

postcss-focus-visible@^6.0.4:
  version "6.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-focus-visible/-/postcss-focus-visible-6.0.4.tgz#50c9ea9afa0ee657fb75635fabad25e18d76bf9e"
  integrity sha1-UMnqmvoO5lf7dWNfq60l4Y12v54=
  dependencies:
    postcss-selector-parser "^6.0.9"

postcss-focus-within@^5.0.4:
  version "5.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-focus-within/-/postcss-focus-within-5.0.4.tgz#5b1d2ec603195f3344b716c0b75f61e44e8d2e20"
  integrity sha1-Wx0uxgMZXzNEtxbAt19h5E6NLiA=
  dependencies:
    postcss-selector-parser "^6.0.9"

postcss-font-variant@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-font-variant/-/postcss-font-variant-5.0.0.tgz#efd59b4b7ea8bb06127f2d031bfbb7f24d32fa66"
  integrity sha1-79WbS36ouwYSfy0DG/u38k0y+mY=

postcss-gap-properties@^3.0.5:
  version "3.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-gap-properties/-/postcss-gap-properties-3.0.5.tgz#f7e3cddcf73ee19e94ccf7cb77773f9560aa2fff"
  integrity sha1-9+PN3Pc+4Z6UzPfLd3c/lWCqL/8=

postcss-image-set-function@^4.0.7:
  version "4.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-image-set-function/-/postcss-image-set-function-4.0.7.tgz#08353bd756f1cbfb3b6e93182c7829879114481f"
  integrity sha1-CDU711bxy/s7bpMYLHgph5EUSB8=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-initial@^4.0.1:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-initial/-/postcss-initial-4.0.1.tgz#529f735f72c5724a0fb30527df6fb7ac54d7de42"
  integrity sha1-Up9zX3LFckoPswUn32+3rFTX3kI=

postcss-lab-function@^4.2.1:
  version "4.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-lab-function/-/postcss-lab-function-4.2.1.tgz#6fe4c015102ff7cd27d1bd5385582f67ebdbdc98"
  integrity sha1-b+TAFRAv980n0b1ThVgvZ+vb3Jg=
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

postcss-loader@^6.2.1:
  version "6.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-loader/-/postcss-loader-6.2.1.tgz#0895f7346b1702103d30fdc66e4d494a93c008ef"
  integrity sha1-CJX3NGsXAhA9MP3Gbk1JSpPACO8=
  dependencies:
    cosmiconfig "^7.0.0"
    klona "^2.0.5"
    semver "^7.3.5"

postcss-logical@^5.0.4:
  version "5.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-logical/-/postcss-logical-5.0.4.tgz#ec75b1ee54421acc04d5921576b7d8db6b0e6f73"
  integrity sha1-7HWx7lRCGswE1ZIVdrfY22sOb3M=

postcss-media-minmax@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-media-minmax/-/postcss-media-minmax-5.0.0.tgz#7140bddec173e2d6d657edbd8554a55794e2a5b5"
  integrity sha1-cUC93sFz4tbWV+29hVSlV5TipbU=

postcss-merge-longhand@^5.1.7:
  version "5.1.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-merge-longhand/-/postcss-merge-longhand-5.1.7.tgz#24a1bdf402d9ef0e70f568f39bdc0344d568fb16"
  integrity sha1-JKG99ALZ7w5w9Wjzm9wDRNVo+xY=
  dependencies:
    postcss-value-parser "^4.2.0"
    stylehacks "^5.1.1"

postcss-merge-rules@^5.1.4:
  version "5.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-merge-rules/-/postcss-merge-rules-5.1.4.tgz#2f26fa5cacb75b1402e213789f6766ae5e40313c"
  integrity sha1-Lyb6XKy3WxQC4hN4n2dmrl5AMTw=
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"
    cssnano-utils "^3.1.0"
    postcss-selector-parser "^6.0.5"

postcss-minify-font-values@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-minify-font-values/-/postcss-minify-font-values-5.1.0.tgz#f1df0014a726083d260d3bd85d7385fb89d1f01b"
  integrity sha1-8d8AFKcmCD0mDTvYXXOF+4nR8Bs=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-minify-gradients@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-minify-gradients/-/postcss-minify-gradients-5.1.1.tgz#f1fe1b4f498134a5068240c2f25d46fcd236ba2c"
  integrity sha1-8f4bT0mBNKUGgkDC8l1G/NI2uiw=
  dependencies:
    colord "^2.9.1"
    cssnano-utils "^3.1.0"
    postcss-value-parser "^4.2.0"

postcss-minify-params@^5.1.4:
  version "5.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-minify-params/-/postcss-minify-params-5.1.4.tgz#c06a6c787128b3208b38c9364cfc40c8aa5d7352"
  integrity sha1-wGpseHEosyCLOMk2TPxAyKpdc1I=
  dependencies:
    browserslist "^4.21.4"
    cssnano-utils "^3.1.0"
    postcss-value-parser "^4.2.0"

postcss-minify-selectors@^5.2.1:
  version "5.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-minify-selectors/-/postcss-minify-selectors-5.2.1.tgz#d4e7e6b46147b8117ea9325a915a801d5fe656c6"
  integrity sha1-1OfmtGFHuBF+qTJakVqAHV/mVsY=
  dependencies:
    postcss-selector-parser "^6.0.5"

postcss-modules-extract-imports@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.1.0.tgz#b4497cb85a9c0c4b5aabeb759bb25e8d89f15002"
  integrity sha1-tEl8uFqcDEtaq+t1m7JejYnxUAI=

postcss-modules-local-by-default@^4.0.5:
  version "4.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.0.5.tgz#f1b9bd757a8edf4d8556e8d0f4f894260e3df78f"
  integrity sha1-8bm9dXqO302FVujQ9PiUJg49948=
  dependencies:
    icss-utils "^5.0.0"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^3.2.0:
  version "3.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-modules-scope/-/postcss-modules-scope-3.2.0.tgz#a43d28289a169ce2c15c00c4e64c0858e43457d5"
  integrity sha1-pD0oKJoWnOLBXADE5kwIWOQ0V9U=
  dependencies:
    postcss-selector-parser "^6.0.4"

postcss-modules-values@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz#d7c5e7e68c3bb3c9b27cbf48ca0bb3ffb4602c9c"
  integrity sha1-18Xn5ow7s8myfL9Iyguz/7RgLJw=
  dependencies:
    icss-utils "^5.0.0"

postcss-nesting@^10.2.0:
  version "10.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-nesting/-/postcss-nesting-10.2.0.tgz#0b12ce0db8edfd2d8ae0aaf86427370b898890be"
  integrity sha1-CxLODbjt/S2K4Kr4ZCc3C4mIkL4=
  dependencies:
    "@csstools/selector-specificity" "^2.0.0"
    postcss-selector-parser "^6.0.10"

postcss-normalize-charset@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-charset/-/postcss-normalize-charset-5.1.0.tgz#9302de0b29094b52c259e9b2cf8dc0879879f0ed"
  integrity sha1-kwLeCykJS1LCWemyz43Ah5h58O0=

postcss-normalize-display-values@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-display-values/-/postcss-normalize-display-values-5.1.0.tgz#72abbae58081960e9edd7200fcf21ab8325c3da8"
  integrity sha1-cqu65YCBlg6e3XIA/PIauDJcPag=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-positions@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-positions/-/postcss-normalize-positions-5.1.1.tgz#ef97279d894087b59325b45c47f1e863daefbb92"
  integrity sha1-75cnnYlAh7WTJbRcR/HoY9rvu5I=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-repeat-style@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-5.1.1.tgz#e9eb96805204f4766df66fd09ed2e13545420fb2"
  integrity sha1-6euWgFIE9HZt9m/QntLhNUVCD7I=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-string@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-string/-/postcss-normalize-string-5.1.0.tgz#411961169e07308c82c1f8c55f3e8a337757e228"
  integrity sha1-QRlhFp4HMIyCwfjFXz6KM3dX4ig=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-timing-functions@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-5.1.0.tgz#d5614410f8f0b2388e9f240aa6011ba6f52dafbb"
  integrity sha1-1WFEEPjwsjiOnyQKpgEbpvUtr7s=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-unicode@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-unicode/-/postcss-normalize-unicode-5.1.1.tgz#f67297fca3fea7f17e0d2caa40769afc487aa030"
  integrity sha1-9nKX/KP+p/F+DSyqQHaa/Eh6oDA=
  dependencies:
    browserslist "^4.21.4"
    postcss-value-parser "^4.2.0"

postcss-normalize-url@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-url/-/postcss-normalize-url-5.1.0.tgz#ed9d88ca82e21abef99f743457d3729a042adcdc"
  integrity sha1-7Z2IyoLiGr75n3Q0V9NymgQq3Nw=
  dependencies:
    normalize-url "^6.0.1"
    postcss-value-parser "^4.2.0"

postcss-normalize-whitespace@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-whitespace/-/postcss-normalize-whitespace-5.1.1.tgz#08a1a0d1ffa17a7cc6efe1e6c9da969cc4493cfa"
  integrity sha1-CKGg0f+henzG7+HmydqWnMRJPPo=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize@^10.0.1:
  version "10.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize/-/postcss-normalize-10.0.1.tgz#464692676b52792a06b06880a176279216540dd7"
  integrity sha1-RkaSZ2tSeSoGsGiAoXYnkhZUDdc=
  dependencies:
    "@csstools/normalize.css" "*"
    postcss-browser-comments "^4"
    sanitize.css "*"

postcss-opacity-percentage@^1.1.2:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-opacity-percentage/-/postcss-opacity-percentage-1.1.3.tgz#5b89b35551a556e20c5d23eb5260fbfcf5245da6"
  integrity sha1-W4mzVVGlVuIMXSPrUmD7/PUkXaY=

postcss-ordered-values@^5.1.3:
  version "5.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-ordered-values/-/postcss-ordered-values-5.1.3.tgz#b6fd2bd10f937b23d86bc829c69e7732ce76ea38"
  integrity sha1-tv0r0Q+TeyPYa8gpxp53Ms526jg=
  dependencies:
    cssnano-utils "^3.1.0"
    postcss-value-parser "^4.2.0"

postcss-overflow-shorthand@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-overflow-shorthand/-/postcss-overflow-shorthand-3.0.4.tgz#7ed6486fec44b76f0eab15aa4866cda5d55d893e"
  integrity sha1-ftZIb+xEt28OqxWqSGbNpdVdiT4=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-page-break@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-page-break/-/postcss-page-break-3.0.4.tgz#7fbf741c233621622b68d435babfb70dd8c1ee5f"
  integrity sha1-f790HCM2IWIraNQ1ur+3DdjB7l8=

postcss-place@^7.0.5:
  version "7.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-place/-/postcss-place-7.0.5.tgz#95dbf85fd9656a3a6e60e832b5809914236986c4"
  integrity sha1-ldv4X9llajpuYOgytYCZFCNphsQ=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-preset-env@^7.0.1:
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-preset-env/-/postcss-preset-env-7.8.3.tgz#2a50f5e612c3149cc7af75634e202a5b2ad4f1e2"
  integrity sha1-KlD15hLDFJzHr3VjTiAqWyrU8eI=
  dependencies:
    "@csstools/postcss-cascade-layers" "^1.1.1"
    "@csstools/postcss-color-function" "^1.1.1"
    "@csstools/postcss-font-format-keywords" "^1.0.1"
    "@csstools/postcss-hwb-function" "^1.0.2"
    "@csstools/postcss-ic-unit" "^1.0.1"
    "@csstools/postcss-is-pseudo-class" "^2.0.7"
    "@csstools/postcss-nested-calc" "^1.0.0"
    "@csstools/postcss-normalize-display-values" "^1.0.1"
    "@csstools/postcss-oklab-function" "^1.1.1"
    "@csstools/postcss-progressive-custom-properties" "^1.3.0"
    "@csstools/postcss-stepped-value-functions" "^1.0.1"
    "@csstools/postcss-text-decoration-shorthand" "^1.0.0"
    "@csstools/postcss-trigonometric-functions" "^1.0.2"
    "@csstools/postcss-unset-value" "^1.0.2"
    autoprefixer "^10.4.13"
    browserslist "^4.21.4"
    css-blank-pseudo "^3.0.3"
    css-has-pseudo "^3.0.4"
    css-prefers-color-scheme "^6.0.3"
    cssdb "^7.1.0"
    postcss-attribute-case-insensitive "^5.0.2"
    postcss-clamp "^4.1.0"
    postcss-color-functional-notation "^4.2.4"
    postcss-color-hex-alpha "^8.0.4"
    postcss-color-rebeccapurple "^7.1.1"
    postcss-custom-media "^8.0.2"
    postcss-custom-properties "^12.1.10"
    postcss-custom-selectors "^6.0.3"
    postcss-dir-pseudo-class "^6.0.5"
    postcss-double-position-gradients "^3.1.2"
    postcss-env-function "^4.0.6"
    postcss-focus-visible "^6.0.4"
    postcss-focus-within "^5.0.4"
    postcss-font-variant "^5.0.0"
    postcss-gap-properties "^3.0.5"
    postcss-image-set-function "^4.0.7"
    postcss-initial "^4.0.1"
    postcss-lab-function "^4.2.1"
    postcss-logical "^5.0.4"
    postcss-media-minmax "^5.0.0"
    postcss-nesting "^10.2.0"
    postcss-opacity-percentage "^1.1.2"
    postcss-overflow-shorthand "^3.0.4"
    postcss-page-break "^3.0.4"
    postcss-place "^7.0.5"
    postcss-pseudo-class-any-link "^7.1.6"
    postcss-replace-overflow-wrap "^4.0.0"
    postcss-selector-not "^6.0.1"
    postcss-value-parser "^4.2.0"

postcss-pseudo-class-any-link@^7.1.6:
  version "7.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-pseudo-class-any-link/-/postcss-pseudo-class-any-link-7.1.6.tgz#2693b221902da772c278def85a4d9a64b6e617ab"
  integrity sha1-JpOyIZAtp3LCeN74Wk2aZLbmF6s=
  dependencies:
    postcss-selector-parser "^6.0.10"

postcss-reduce-initial@^5.1.2:
  version "5.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-reduce-initial/-/postcss-reduce-initial-5.1.2.tgz#798cd77b3e033eae7105c18c9d371d989e1382d6"
  integrity sha1-eYzXez4DPq5xBcGMnTcdmJ4TgtY=
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"

postcss-reduce-transforms@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-reduce-transforms/-/postcss-reduce-transforms-5.1.0.tgz#333b70e7758b802f3dd0ddfe98bb1ccfef96b6e9"
  integrity sha1-Mztw53WLgC890N3+mLscz++Wtuk=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-replace-overflow-wrap@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-replace-overflow-wrap/-/postcss-replace-overflow-wrap-4.0.0.tgz#d2df6bed10b477bf9c52fab28c568b4b29ca4319"
  integrity sha1-0t9r7RC0d7+cUvqyjFaLSynKQxk=

postcss-scss@^4.0.6:
  version "4.0.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-scss/-/postcss-scss-4.0.9.tgz#a03c773cd4c9623cb04ce142a52afcec74806685"
  integrity sha1-oDx3PNTJYjywTOFCpSr87HSAZoU=

postcss-selector-not@^6.0.1:
  version "6.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-selector-not/-/postcss-selector-not-6.0.1.tgz#8f0a709bf7d4b45222793fc34409be407537556d"
  integrity sha1-jwpwm/fUtFIieT/DRAm+QHU3VW0=
  dependencies:
    postcss-selector-parser "^6.0.10"

postcss-selector-parser@^6.0.10, postcss-selector-parser@^6.0.2, postcss-selector-parser@^6.0.4, postcss-selector-parser@^6.0.5, postcss-selector-parser@^6.0.9:
  version "6.0.16"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-selector-parser/-/postcss-selector-parser-6.0.16.tgz#3b88b9f5c5abd989ef4e2fc9ec8eedd34b20fb04"
  integrity sha1-O4i59cWr2YnvTi/J7I7t00sg+wQ=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-svgo@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-svgo/-/postcss-svgo-5.1.0.tgz#0a317400ced789f233a28826e77523f15857d80d"
  integrity sha1-CjF0AM7XifIzoogm53Uj8VhX2A0=
  dependencies:
    postcss-value-parser "^4.2.0"
    svgo "^2.7.0"

postcss-unique-selectors@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-unique-selectors/-/postcss-unique-selectors-5.1.1.tgz#a9f273d1eacd09e9aa6088f4b0507b18b1b541b6"
  integrity sha1-qfJz0erNCemqYIj0sFB7GLG1QbY=
  dependencies:
    postcss-selector-parser "^6.0.5"

postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz#723c09920836ba6d3e5af019f92bc0971c02e514"
  integrity sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=

postcss@^8.2.14, postcss@^8.4.17, postcss@^8.4.33, postcss@^8.4.4:
  version "8.4.38"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss/-/postcss-8.4.38.tgz#b387d533baf2054288e337066d81c6bee9db9e0e"
  integrity sha1-s4fVM7ryBUKI4zcGbYHGvunbng4=
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.0.0"
    source-map-js "^1.2.0"

pretty-error@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/pretty-error/-/pretty-error-4.0.0.tgz#90a703f46dd7234adb46d0f84823e9d1cb8f10d6"
  integrity sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY=
  dependencies:
    lodash "^4.17.20"
    renderkid "^3.0.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/process-nextick-args/-/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

prompts@^2.4.2:
  version "2.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/prompts/-/prompts-2.4.2.tgz#7b57e73b3a48029ad10ebd44f74b01722a4cb069"
  integrity sha1-e1fnOzpIAprRDr1E90sBcipMsGk=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@^15.6.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/prop-types/-/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

property-expr@^2.0.5:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/property-expr/-/property-expr-2.0.6.tgz#f77bc00d5928a6c748414ad12882e83f24aec1e8"
  integrity sha1-93vADVkopsdIQUrRKILoPySuweg=

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/proxy-addr/-/proxy-addr-2.0.7.tgz#f19fe69ceab311eeb94b42e70e8c2070f9ba1025"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

punycode@^2.1.0:
  version "2.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/punycode/-/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

q@^1.1.2:
  version "1.5.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/q/-/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qs@6.11.0:
  version "6.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/qs/-/qs-6.11.0.tgz#fd0d963446f7a65e1367e01abd85429453f0c37a"
  integrity sha1-/Q2WNEb3pl4TZ+AavYVClFPww3o=
  dependencies:
    side-channel "^1.0.4"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/queue-microtask/-/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/randombytes/-/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/range-parser/-/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.5.2:
  version "2.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/raw-body/-/raw-body-2.5.2.tgz#99febd83b90e08975087e8f1f9419a149366b68a"
  integrity sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

react-dev-utils@^12.0.1:
  version "12.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-dev-utils/-/react-dev-utils-12.0.1.tgz#ba92edb4a1f379bd46ccd6bcd4e7bc398df33e73"
  integrity sha1-upLttKHzeb1GzNa81Oe8OY3zPnM=
  dependencies:
    "@babel/code-frame" "^7.16.0"
    address "^1.1.2"
    browserslist "^4.18.1"
    chalk "^4.1.2"
    cross-spawn "^7.0.3"
    detect-port-alt "^1.1.6"
    escape-string-regexp "^4.0.0"
    filesize "^8.0.6"
    find-up "^5.0.0"
    fork-ts-checker-webpack-plugin "^6.5.0"
    global-modules "^2.0.0"
    globby "^11.0.4"
    gzip-size "^6.0.0"
    immer "^9.0.7"
    is-root "^2.1.0"
    loader-utils "^3.2.0"
    open "^8.4.0"
    pkg-up "^3.1.0"
    prompts "^2.4.2"
    react-error-overlay "^6.0.11"
    recursive-readdir "^2.2.2"
    shell-quote "^1.7.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

react-dom@^18.2.0:
  version "18.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-dom/-/react-dom-18.3.1.tgz#c2265d79511b57d479b3dd3fdfa51536494c5cb4"
  integrity sha1-wiZdeVEbV9R5s90/36UVNklMXLQ=
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.2"

react-error-overlay@^6.0.11:
  version "6.0.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-error-overlay/-/react-error-overlay-6.0.11.tgz#92835de5841c5cf08ba00ddd2d677b6d17ff9adb"
  integrity sha1-koNd5YQcXPCLoA3dLWd7bRf/mts=

react-fast-compare@^2.0.1:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-fast-compare/-/react-fast-compare-2.0.4.tgz#e84b4d455b0fec113e0402c329352715196f81f9"
  integrity sha1-6EtNRVsP7BE+BALDKTUnFRlvgfk=

react-infinite-scroll-component@^6.1.0:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-infinite-scroll-component/-/react-infinite-scroll-component-6.1.0.tgz#7e511e7aa0f728ac3e51f64a38a6079ac522407f"
  integrity sha1-flEeeqD3KKw+UfZKOKYHmsUiQH8=
  dependencies:
    throttle-debounce "^2.1.0"

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-is/-/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^18.3.1:
  version "18.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-is/-/react-is-18.3.1.tgz#e83557dc12eae63a99e003a46388b1dcbb44db7e"
  integrity sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=

react-refresh@^0.11.0:
  version "0.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-refresh/-/react-refresh-0.11.0.tgz#77198b944733f0f1f1a90e791de4541f9f074046"
  integrity sha1-dxmLlEcz8PHxqQ55HeRUH58HQEY=

react-router-dom@^6.8.2:
  version "6.23.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-router-dom/-/react-router-dom-6.23.0.tgz#8b80ad92ad28f4dc38972e92d84b4c208150545a"
  integrity sha1-i4Ctkq0o9Nw4ly6S2EtMIIFQVFo=
  dependencies:
    "@remix-run/router" "1.16.0"
    react-router "6.23.0"

react-router@6.23.0:
  version "6.23.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-router/-/react-router-6.23.0.tgz#2f2d7492c66a6bdf760be4c6bdf9e1d672fa154b"
  integrity sha1-Ly10ksZqa992C+TGvfnh1nL6FUs=
  dependencies:
    "@remix-run/router" "1.16.0"

react-transition-group@^4.4.5:
  version "4.4.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-transition-group/-/react-transition-group-4.4.5.tgz#e53d4e3f3344da8521489fbef8f2581d42becdd1"
  integrity sha1-5T1OPzNE2oUhSJ+++PJYHUK+zdE=
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react@^18.2.0:
  version "18.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react/-/react-18.3.1.tgz#49ab892009c53933625bd16b2533fc754cab2891"
  integrity sha1-SauJIAnFOTNiW9FrJTP8dUyrKJE=
  dependencies:
    loose-envify "^1.1.0"

readable-stream@^2.0.1:
  version "2.3.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/readable-stream/-/readable-stream-2.3.8.tgz#91125e8042bba1b9887f49345f6277027ce8be9b"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6:
  version "3.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/readable-stream/-/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/readdirp/-/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.8.0:
  version "0.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/rechoir/-/rechoir-0.8.0.tgz#49f866e0d32146142da3ad8f0eff352b3215ff22"
  integrity sha1-Sfhm4NMhRhQto62PDv81KzIV/yI=
  dependencies:
    resolve "^1.20.0"

recursive-readdir@^2.2.2:
  version "2.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/recursive-readdir/-/recursive-readdir-2.2.3.tgz#e726f328c0d69153bcabd5c322d3195252379372"
  integrity sha1-5ybzKMDWkVO8q9XDItMZUlI3k3I=
  dependencies:
    minimatch "^3.0.5"

redi-component-utils@^1.0.21:
  version "1.0.21"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redi-component-utils/-/redi-component-utils-1.0.21.tgz#ce13b8a2c0bb49bec17076a1aeb5ed2fcc27a55f"
  integrity sha1-zhO4osC7Sb7BcHahrrXtL8wnpV8=

redi-formik-material@^2.0.12:
  version "2.0.12"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redi-formik-material/-/redi-formik-material-2.0.12.tgz#9cc03523942a99f048e2066d7d9cd4e578c8f4fd"
  integrity sha1-nMA1I5QqmfBI4gZtfZzU5XjI9P0=

redi-http@^3.0.6:
  version "3.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redi-http/-/redi-http-3.0.6.tgz#e972b231d233f1560bc177b2c0bf633537982dbc"
  integrity sha1-6XKyMdIz8VYLwXeywL9jNTeYLbw=

redi-security-components@2.0.4:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redi-security-components/-/redi-security-components-2.0.4.tgz#c0b093fe5e04226db53ebff5ac82a98f3839ef21"
  integrity sha1-wLCT/l4EIm21Pr/1rIKpjzg57yE=

redi-ui-utils@^2.0.0:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redi-ui-utils/-/redi-ui-utils-2.0.2.tgz#e97ff4c3e8f205669fcdfd89164b5739d35e58d7"
  integrity sha1-6X/0w+jyBWafzf2JFktXOdNeWNc=

regenerate-unicode-properties@^10.1.0:
  version "10.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.1.tgz#6b0e05489d9076b04c436f318d9b067bba459480"
  integrity sha1-aw4FSJ2QdrBMQ28xjZsGe7pFlIA=
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regenerate/-/regenerate-1.4.2.tgz#b9346d8827e8f5a32f7ba29637d398b69014848a"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.14.0:
  version "0.14.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz#356ade10263f685dda125100cd862c1db895327f"
  integrity sha1-NWreECY/aF3aElEAzYYsHbiVMn8=

regenerator-transform@^0.15.2:
  version "0.15.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regenerator-transform/-/regenerator-transform-0.15.2.tgz#5bbae58b522098ebdf09bca2f83838929001c7a4"
  integrity sha1-W7rli1IgmOvfCbyi+Dg4kpABx6Q=
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-parser@^2.2.11:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regex-parser/-/regex-parser-2.3.0.tgz#4bb61461b1a19b8b913f3960364bb57887f920ee"
  integrity sha1-S7YUYbGhm4uRPzlgNku1eIf5IO4=

regexp.prototype.flags@^1.5.2:
  version "1.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regexp.prototype.flags/-/regexp.prototype.flags-1.5.2.tgz#138f644a3350f981a858c44f6bb1a61ff59be334"
  integrity sha1-E49kSjNQ+YGoWMRPa7GmH/Wb4zQ=
  dependencies:
    call-bind "^1.0.6"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    set-function-name "^2.0.1"

regexpu-core@^5.3.1:
  version "5.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regexpu-core/-/regexpu-core-5.3.2.tgz#11a2b06884f3527aec3e93dbbf4a3b958a95546b"
  integrity sha1-EaKwaITzUnrsPpPbv0o7lYqVVGs=
  dependencies:
    "@babel/regjsgen" "^0.8.0"
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.1.0"
    regjsparser "^0.9.1"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsparser@^0.9.1:
  version "0.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regjsparser/-/regjsparser-0.9.1.tgz#272d05aa10c7c1f67095b1ff0addae8442fc5709"
  integrity sha1-Jy0FqhDHwfZwlbH/Ct2uhEL8Vwk=
  dependencies:
    jsesc "~0.5.0"

relateurl@^0.2.7:
  version "0.2.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/relateurl/-/relateurl-0.2.7.tgz#54dbf377e51440aca90a4cd274600d3ff2d888a9"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

renderkid@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/renderkid/-/renderkid-3.0.0.tgz#5fd823e4d6951d37358ecc9a58b1f06836b6268a"
  integrity sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo=
  dependencies:
    css-select "^4.1.3"
    dom-converter "^0.2.0"
    htmlparser2 "^6.1.0"
    lodash "^4.17.21"
    strip-ansi "^6.0.1"

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/require-from-string/-/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/requires-port/-/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

reselect@^4.1.8:
  version "4.1.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/reselect/-/reselect-4.1.8.tgz#3f5dc671ea168dccdeb3e141236f69f02eaec524"
  integrity sha1-P13GceoWjczes+FBI29p8C6uxSQ=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/resolve-cwd/-/resolve-cwd-3.0.0.tgz#0f0075f1bb2544766cf73ba6a6e2adfebcb13f2d"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/resolve-from/-/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/resolve-from/-/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-url-loader@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/resolve-url-loader/-/resolve-url-loader-5.0.0.tgz#ee3142fb1f1e0d9db9524d539cfa166e9314f795"
  integrity sha1-7jFC+x8eDZ25Uk1TnPoWbpMU95U=
  dependencies:
    adjust-sourcemap-loader "^4.0.0"
    convert-source-map "^1.7.0"
    loader-utils "^2.0.0"
    postcss "^8.2.14"
    source-map "0.6.1"

resolve@^1.14.2, resolve@^1.19.0, resolve@^1.20.0:
  version "1.22.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/resolve/-/resolve-1.22.8.tgz#b6c87a9f2aa06dfab52e3d70ac8cde321fa5a48d"
  integrity sha1-tsh6nyqgbfq1Lj1wrIzeMh+lpI0=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

retry@^0.13.1:
  version "0.13.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/retry/-/retry-0.13.1.tgz#185b1587acf67919d63b357349e03537b2484658"
  integrity sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/reusify/-/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/rimraf/-/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/run-parallel/-/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

safe-array-concat@^1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/safe-array-concat/-/safe-array-concat-1.1.2.tgz#81d77ee0c4e8b863635227c721278dd524c20edb"
  integrity sha1-gdd+4MTouGNjUifHISeN1STCDts=
  dependencies:
    call-bind "^1.0.7"
    get-intrinsic "^1.2.4"
    has-symbols "^1.0.3"
    isarray "^2.0.5"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/safe-buffer/-/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@5.2.1, safe-buffer@>=5.1.0, safe-buffer@^5.1.0, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/safe-buffer/-/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex-test@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/safe-regex-test/-/safe-regex-test-1.0.3.tgz#a5b4c0f06e0ab50ea2c395c14d8371232924c377"
  integrity sha1-pbTA8G4KtQ6iw5XBTYNxIykkw3c=
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-regex "^1.1.4"

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/safer-buffer/-/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sanitize.css@*:
  version "13.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sanitize.css/-/sanitize.css-13.0.0.tgz#2675553974b27964c75562ade3bd85d79879f173"
  integrity sha1-JnVVOXSyeWTHVWKt472F15h58XM=

sass-loader@^12.3.0:
  version "12.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sass-loader/-/sass-loader-12.6.0.tgz#5148362c8e2cdd4b950f3c63ac5d16dbfed37bcb"
  integrity sha1-UUg2LI4s3UuVDzxjrF0W2/7Te8s=
  dependencies:
    klona "^2.0.4"
    neo-async "^2.6.2"

sass@^1.58.3:
  version "1.77.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sass/-/sass-1.77.0.tgz#e736c69aff9fae4a4e6dae60a979eee9c942f321"
  integrity sha1-5zbGmv+frkpOba5gqXnu6clC8yE=
  dependencies:
    chokidar ">=3.0.0 <4.0.0"
    immutable "^4.0.0"
    source-map-js ">=0.6.2 <2.0.0"

sax@~1.2.4:
  version "1.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sax/-/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

scheduler@^0.23.2:
  version "0.23.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/scheduler/-/scheduler-0.23.2.tgz#414ba64a3b282892e944cf2108ecc078d115cdc3"
  integrity sha1-QUumSjsoKJLpRM8hCOzAeNEVzcM=
  dependencies:
    loose-envify "^1.1.0"

schema-utils@2.7.0:
  version "2.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/schema-utils/-/schema-utils-2.7.0.tgz#17151f76d8eae67fbbf77960c33c676ad9f4efc7"
  integrity sha1-FxUfdtjq5n+793lgwzxnatn078c=
  dependencies:
    "@types/json-schema" "^7.0.4"
    ajv "^6.12.2"
    ajv-keywords "^3.4.1"

schema-utils@^2.6.5:
  version "2.7.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/schema-utils/-/schema-utils-2.7.1.tgz#1ca4f32d1b24c590c203b8e7a50bf0ea4cd394d7"
  integrity sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^3.0.0, schema-utils@^3.1.1, schema-utils@^3.2.0:
  version "3.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/schema-utils/-/schema-utils-3.3.0.tgz#f50a88877c3c01652a15b622ae9e9795df7a60fe"
  integrity sha1-9QqIh3w8AWUqFbYirp6Xld96YP4=
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^4.0.0:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/schema-utils/-/schema-utils-4.2.0.tgz#70d7c93e153a273a805801882ebd3bff20d89c8b"
  integrity sha1-cNfJPhU6JzqAWAGILr07/yDYnIs=
  dependencies:
    "@types/json-schema" "^7.0.9"
    ajv "^8.9.0"
    ajv-formats "^2.1.1"
    ajv-keywords "^5.1.0"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/select-hose/-/select-hose-2.0.0.tgz#625d8658f865af43ec962bfc376a37359a4994ca"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selfsigned@^2.1.1:
  version "2.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/selfsigned/-/selfsigned-2.4.1.tgz#560d90565442a3ed35b674034cec4e95dceb4ae0"
  integrity sha1-Vg2QVlRCo+01tnQDTOxOldzrSuA=
  dependencies:
    "@types/node-forge" "^1.3.0"
    node-forge "^1"

semver@^6.0.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/semver/-/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.3.2, semver@^7.3.5, semver@^7.5.4:
  version "7.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/semver/-/semver-7.6.0.tgz#1a46a4db4bffcccd97b743b5005c8325f23d4e2d"
  integrity sha1-Gkak20v/zM2Xt0O1AFyDJfI9Ti0=
  dependencies:
    lru-cache "^6.0.0"

send@0.18.0:
  version "0.18.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/send/-/send-0.18.0.tgz#670167cc654b05f5aa4a767f9113bb371bc706be"
  integrity sha1-ZwFnzGVLBfWqSnZ/kRO7NxvHBr4=
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^6.0.0, serialize-javascript@^6.0.1:
  version "6.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/serialize-javascript/-/serialize-javascript-6.0.2.tgz#defa1e055c83bf6d59ea805d8da862254eb6a6c2"
  integrity sha1-3voeBVyDv21Z6oBdjahiJU62psI=
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/serve-index/-/serve-index-1.9.1.tgz#d3768d69b1e7d82e5ce050fff5b453bea12a9239"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.15.0:
  version "1.15.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/serve-static/-/serve-static-1.15.0.tgz#faaef08cffe0a1a62f60cad0c4e513cff0ac9540"
  integrity sha1-+q7wjP/goaYvYMrQxOUTz/CslUA=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/set-function-length/-/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.1:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/set-function-name/-/set-function-name-2.0.2.tgz#16a705c5a0dc2f5e638ca96d8a8cd4e1c2b90985"
  integrity sha1-FqcFxaDcL15jjKltiozU4cK5CYU=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/setprototypeof/-/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/setprototypeof/-/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/shallow-clone/-/shallow-clone-3.0.1.tgz#8f2981ad92531f55035b01fb230769a40e02efa3"
  integrity sha1-jymBrZJTH1UDWwH7IwdppA4C76M=
  dependencies:
    kind-of "^6.0.2"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/shebang-command/-/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/shebang-regex/-/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@^1.7.3, shell-quote@^1.8.1:
  version "1.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/shell-quote/-/shell-quote-1.8.1.tgz#6dbf4db75515ad5bac63b4f1894c3a154c766680"
  integrity sha1-bb9Nt1UVrVusY7TxiUw6FUx2ZoA=

side-channel@^1.0.4:
  version "1.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/side-channel/-/side-channel-1.0.6.tgz#abd25fb7cd24baf45466406b1096b7831c9215f2"
  integrity sha1-q9Jft80kuvRUZkBrEJa3gxySFfI=
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    object-inspect "^1.13.1"

signal-exit@^3.0.3:
  version "3.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/signal-exit/-/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sisteransi/-/sisteransi-1.0.5.tgz#134d681297756437cc05ca01370d3a7a571075ed"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/slash/-/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

sockjs@^0.3.24:
  version "0.3.24"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sockjs/-/sockjs-0.3.24.tgz#c9bc8995f33a111bea0395ec30aa3206bdb5ccce"
  integrity sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4=
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

"source-map-js@>=0.6.2 <2.0.0", source-map-js@^1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map-js/-/source-map-js-1.2.0.tgz#16b809c162517b5b8c3e7dcd315a2a5c2612b2af"
  integrity sha1-FrgJwWJRe1uMPn3NMVoqXCYSsq8=

source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map-support/-/source-map-support-0.5.21.tgz#04fe7c7f9e1ed2d662233c28cb2b35b9f63f6e4f"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@0.6.1, source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0:
  version "0.6.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map/-/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.5.7:
  version "0.5.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map/-/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.7.3:
  version "0.7.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map/-/source-map-0.7.4.tgz#a9bbe705c9d8846f4e08ff6765acf0f1b0898656"
  integrity sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY=

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/spdy-transport/-/spdy-transport-3.0.0.tgz#00d4863a6400ad75df93361a1608605e5dcdcf31"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/spdy/-/spdy-4.0.2.tgz#b74f466203a3eda452c02492b91fb9e84a27677b"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

stable@^0.1.8:
  version "0.1.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/stable/-/stable-0.1.8.tgz#836eb3c8382fe2936feaf544631017ce7d47a3cf"
  integrity sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=

stackframe@^1.3.4:
  version "1.3.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/stackframe/-/stackframe-1.3.4.tgz#b881a004c8c149a5e8efef37d51b16e412943310"
  integrity sha1-uIGgBMjBSaXo7+831RsW5BKUMxA=

statuses@2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/statuses/-/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

"statuses@>= 1.4.0 < 2":
  version "1.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/statuses/-/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

string.prototype.trim@^1.2.9:
  version "1.2.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/string.prototype.trim/-/string.prototype.trim-1.2.9.tgz#b6fa326d72d2c78b6df02f7759c73f8f6274faa4"
  integrity sha1-tvoybXLSx4tt8C93Wcc/j2J0+qQ=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.0"
    es-object-atoms "^1.0.0"

string.prototype.trimend@^1.0.8:
  version "1.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.8.tgz#3651b8513719e8a9f48de7f2f77640b26652b229"
  integrity sha1-NlG4UTcZ6Kn0jefy93ZAsmZSsik=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz#7ee834dda8c7c17eff3118472bb35bfedaa34dde"
  integrity sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/string_decoder/-/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/string_decoder/-/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/strip-ansi/-/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/strip-final-newline/-/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

style-loader@^3.3.1:
  version "3.3.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/style-loader/-/style-loader-3.3.4.tgz#f30f786c36db03a45cbd55b6a70d930c479090e7"
  integrity sha1-8w94bDbbA6RcvVW2pw2TDEeQkOc=

stylehacks@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/stylehacks/-/stylehacks-5.1.1.tgz#7934a34eb59d7152149fa69d6e9e56f2fc34bcc9"
  integrity sha1-eTSjTrWdcVIUn6adbp5W8vw0vMk=
  dependencies:
    browserslist "^4.21.4"
    postcss-selector-parser "^6.0.4"

stylis@4.2.0:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/stylis/-/stylis-4.2.0.tgz#79daee0208964c8fe695a42fcffcac633a211a51"
  integrity sha1-edruAgiWTI/mlaQvz/ysYzohGlE=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/supports-color/-/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/supports-color/-/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/supports-color/-/supports-color-8.1.1.tgz#cd6fc17e28500cff56c1b86c0a7fd4a54a73005c"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

svg-parser@^2.0.2:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/svg-parser/-/svg-parser-2.0.4.tgz#fdc2e29e13951736140b76cb122c8ee6630eb6b5"
  integrity sha1-/cLinhOVFzYUC3bLEiyO5mMOtrU=

svgo@^1.2.2:
  version "1.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/svgo/-/svgo-1.3.2.tgz#b6dc511c063346c9e415b81e43401145b96d4167"
  integrity sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc=
  dependencies:
    chalk "^2.4.1"
    coa "^2.0.2"
    css-select "^2.0.0"
    css-select-base-adapter "^0.1.1"
    css-tree "1.0.0-alpha.37"
    csso "^4.0.2"
    js-yaml "^3.13.1"
    mkdirp "~0.5.1"
    object.values "^1.1.0"
    sax "~1.2.4"
    stable "^0.1.8"
    unquote "~1.1.1"
    util.promisify "~1.0.0"

svgo@^2.7.0:
  version "2.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/svgo/-/svgo-2.8.0.tgz#4ff80cce6710dc2795f0c7c74101e6764cfccd24"
  integrity sha1-T/gMzmcQ3CeV8MfHQQHmdkz8zSQ=
  dependencies:
    "@trysound/sax" "0.2.0"
    commander "^7.2.0"
    css-select "^4.1.3"
    css-tree "^1.1.3"
    csso "^4.2.0"
    picocolors "^1.0.0"
    stable "^0.1.8"

tapable@^1.0.0:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tapable/-/tapable-1.1.3.tgz#a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2"
  integrity sha1-ofzMBrWNth/XpF2i2kT186Pme6I=

tapable@^2.0.0, tapable@^2.1.1, tapable@^2.2.0, tapable@^2.2.1:
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tapable/-/tapable-2.2.1.tgz#1967a73ef4060a82f12ab96af86d52fdb76eeca0"
  integrity sha1-GWenPvQGCoLxKrlq+G1S/bdu7KA=

terser-webpack-plugin@^5.2.5, terser-webpack-plugin@^5.3.10:
  version "5.3.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/terser-webpack-plugin/-/terser-webpack-plugin-5.3.10.tgz#904f4c9193c6fd2a03f693a2150c62a92f40d199"
  integrity sha1-kE9MkZPG/SoD9pOiFQxiqS9A0Zk=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.20"
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.1"
    terser "^5.26.0"

terser@^5.10.0, terser@^5.26.0:
  version "5.31.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/terser/-/terser-5.31.0.tgz#06eef86f17007dbad4593f11a574c7f5eb02c6a1"
  integrity sha1-Bu74bxcAfbrUWT8RpXTH9esCxqE=
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.8.2"
    commander "^2.20.0"
    source-map-support "~0.5.20"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/text-table/-/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

throttle-debounce@^2.1.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/throttle-debounce/-/throttle-debounce-2.3.0.tgz#fd31865e66502071e411817e241465b3e9c372e2"
  integrity sha1-/TGGXmZQIHHkEYF+JBRls+nDcuI=

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/thunky/-/thunky-1.1.0.tgz#5abaf714a9405db0504732bbccd2cedd9ef9537d"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

tiny-case@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tiny-case/-/tiny-case-1.0.3.tgz#d980d66bc72b5d5a9ca86fb7c9ffdb9c898ddd03"
  integrity sha1-2YDWa8crXVqcqG+3yf/bnImN3QM=

tiny-warning@^1.0.2:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tiny-warning/-/tiny-warning-1.0.3.tgz#94a30db453df4c643d0fd566060d60a875d84754"
  integrity sha1-lKMNtFPfTGQ9D9VmBg1gqHXYR1Q=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/to-fast-properties/-/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/to-regex-range/-/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/toidentifier/-/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

toposort@^2.0.2:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/toposort/-/toposort-2.0.2.tgz#ae21768175d1559d48bef35420b2f4962f09c330"
  integrity sha1-riF2gXXRVZ1IvvNUILL0li8JwzA=

tslib@^2.0.0, tslib@^2.0.3:
  version "2.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tslib/-/tslib-2.6.2.tgz#703ac29425e7b37cd6fd456e92404d46d1f3e4ae"
  integrity sha1-cDrClCXns3zW/UVukkBNRtHz5K4=

type-fest@^2.19.0:
  version "2.19.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/type-fest/-/type-fest-2.19.0.tgz#88068015bb33036a598b952e55e9311a60fd3a9b"
  integrity sha1-iAaAFbszA2pZi5UuVekxGmD9Ops=

type-is@~1.6.18:
  version "1.6.18"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/type-is/-/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typed-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/typed-array-buffer/-/typed-array-buffer-1.0.2.tgz#1867c5d83b20fcb5ccf32649e5e2fc7424474ff3"
  integrity sha1-GGfF2Dsg/LXM8yZJ5eL8dCRHT/M=
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    is-typed-array "^1.1.13"

typed-array-byte-length@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/typed-array-byte-length/-/typed-array-byte-length-1.0.1.tgz#d92972d3cff99a3fa2e765a28fcdc0f1d89dec67"
  integrity sha1-2Sly08/5mj+i52Wij83A8did7Gc=
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"

typed-array-byte-offset@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/typed-array-byte-offset/-/typed-array-byte-offset-1.0.2.tgz#f9ec1acb9259f395093e4567eb3c28a580d02063"
  integrity sha1-+eway5JZ85UJPkVn6zwopYDQIGM=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"

typed-array-length@^1.0.6:
  version "1.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/typed-array-length/-/typed-array-length-1.0.6.tgz#57155207c76e64a3457482dfdc1c9d1d3c4c73a3"
  integrity sha1-VxVSB8duZKNFdILf3BydHTxMc6M=
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"

typescript@^4.9.5:
  version "4.9.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/typescript/-/typescript-4.9.5.tgz#095979f9bcc0d09da324d58d03ce8f8374cbe65a"
  integrity sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unbox-primitive/-/unbox-primitive-1.0.2.tgz#29032021057d5e6cdbd08c5129c226dff8ed6f9e"
  integrity sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54=
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

undici-types@~5.26.4:
  version "5.26.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/undici-types/-/undici-types-5.26.5.tgz#bcd539893d00b56e964fd2657a4866b221a65617"
  integrity sha1-vNU5iT0AtW6WT9JlekhmsiGmVhc=

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz#301acdc525631670d39f6146e0e77ff6bbdebddc"
  integrity sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz#54fd16e0ecb167cf04cf1f756bdcc92eba7976c3"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz#cb5fffdcd16a05124f5a4b0bf7c3770208acbbe0"
  integrity sha1-y1//3NFqBRJPWksL98N3Agisu+A=

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz#43d41e3be698bd493ef911077c9b131f827e8ccd"
  integrity sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/universalify/-/universalify-2.0.1.tgz#168efc2180964e6386d061e094df61afe239b18d"
  integrity sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unpipe/-/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unquote@~1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unquote/-/unquote-1.1.1.tgz#8fded7324ec6e88a0ff8b905e7c098cdc086d544"
  integrity sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=

update-browserslist-db@^1.0.13:
  version "1.0.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/update-browserslist-db/-/update-browserslist-db-1.0.15.tgz#60ed9f8cba4a728b7ecf7356f641a31e3a691d97"
  integrity sha1-YO2fjLpKcot+z3NW9kGjHjppHZc=
  dependencies:
    escalade "^3.1.2"
    picocolors "^1.0.0"

uri-js@^4.2.2, uri-js@^4.4.1:
  version "4.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/uri-js/-/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@~1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/util.promisify/-/util.promisify-1.0.1.tgz#6baf7774b80eeb0f7520d8b81d07982a59abbaee"
  integrity sha1-a693dLgO6w91INi4HQeYKlmruu4=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.2"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.0"

utila@~0.4:
  version "0.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/utila/-/utila-0.4.0.tgz#8a16a05d445657a3aea5eecc5b12a4fa5379772c"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/utils-merge/-/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/uuid/-/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

uuid@^9.0.0:
  version "9.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/uuid/-/uuid-9.0.1.tgz#e188d4c8853cc722220392c424cd637f32293f30"
  integrity sha1-4YjUyIU8xyIiA5LEJM1jfzIpPzA=

vary@~1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/vary/-/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

watchpack@^2.4.1:
  version "2.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/watchpack/-/watchpack-2.4.1.tgz#29308f2cac150fa8e4c92f90e0ec954a9fed7fff"
  integrity sha1-KTCPLKwVD6jkyS+Q4OyVSp/tf/8=
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/wbuf/-/wbuf-1.7.3.tgz#c1d8d149316d3ea852848895cb6a0bfe887b87df"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

webpack-cli@^5.1.4:
  version "5.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-cli/-/webpack-cli-5.1.4.tgz#c8e046ba7eaae4911d7e71e2b25b776fcc35759b"
  integrity sha1-yOBGun6q5JEdfnHislt3b8w1dZs=
  dependencies:
    "@discoveryjs/json-ext" "^0.5.0"
    "@webpack-cli/configtest" "^2.1.1"
    "@webpack-cli/info" "^2.0.2"
    "@webpack-cli/serve" "^2.0.5"
    colorette "^2.0.14"
    commander "^10.0.1"
    cross-spawn "^7.0.3"
    envinfo "^7.7.3"
    fastest-levenshtein "^1.0.12"
    import-local "^3.0.2"
    interpret "^3.1.1"
    rechoir "^0.8.0"
    webpack-merge "^5.7.3"

webpack-dev-middleware@^5.3.4:
  version "5.3.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-dev-middleware/-/webpack-dev-middleware-5.3.4.tgz#eb7b39281cbce10e104eb2b8bf2b63fce49a3517"
  integrity sha1-63s5KBy84Q4QTrK4vytj/OSaNRc=
  dependencies:
    colorette "^2.0.10"
    memfs "^3.4.3"
    mime-types "^2.1.31"
    range-parser "^1.2.1"
    schema-utils "^4.0.0"

webpack-dev-server@^4.6.0:
  version "4.15.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-dev-server/-/webpack-dev-server-4.15.2.tgz#9e0c70a42a012560860adb186986da1248333173"
  integrity sha1-ngxwpCoBJWCGCtsYaYbaEkgzMXM=
  dependencies:
    "@types/bonjour" "^3.5.9"
    "@types/connect-history-api-fallback" "^1.3.5"
    "@types/express" "^4.17.13"
    "@types/serve-index" "^1.9.1"
    "@types/serve-static" "^1.13.10"
    "@types/sockjs" "^0.3.33"
    "@types/ws" "^8.5.5"
    ansi-html-community "^0.0.8"
    bonjour-service "^1.0.11"
    chokidar "^3.5.3"
    colorette "^2.0.10"
    compression "^1.7.4"
    connect-history-api-fallback "^2.0.0"
    default-gateway "^6.0.3"
    express "^4.17.3"
    graceful-fs "^4.2.6"
    html-entities "^2.3.2"
    http-proxy-middleware "^2.0.3"
    ipaddr.js "^2.0.1"
    launch-editor "^2.6.0"
    open "^8.0.9"
    p-retry "^4.5.0"
    rimraf "^3.0.2"
    schema-utils "^4.0.0"
    selfsigned "^2.1.1"
    serve-index "^1.9.1"
    sockjs "^0.3.24"
    spdy "^4.0.2"
    webpack-dev-middleware "^5.3.4"
    ws "^8.13.0"

webpack-merge@^5.7.3:
  version "5.10.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-merge/-/webpack-merge-5.10.0.tgz#a3ad5d773241e9c682803abf628d4cd62b8a4177"
  integrity sha1-o61ddzJB6caCgDq/Yo1M1iuKQXc=
  dependencies:
    clone-deep "^4.0.1"
    flat "^5.0.2"
    wildcard "^2.0.0"

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-sources/-/webpack-sources-3.2.3.tgz#2d4daab8451fd4b240cc27055ff6a0c2ccea0cde"
  integrity sha1-LU2quEUf1LJAzCcFX/agwszqDN4=

webpack@^5.64.4:
  version "5.91.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack/-/webpack-5.91.0.tgz#ffa92c1c618d18c878f06892bbdc3373c71a01d9"
  integrity sha1-/6ksHGGNGMh48GiSu9wzc8caAdk=
  dependencies:
    "@types/eslint-scope" "^3.7.3"
    "@types/estree" "^1.0.5"
    "@webassemblyjs/ast" "^1.12.1"
    "@webassemblyjs/wasm-edit" "^1.12.1"
    "@webassemblyjs/wasm-parser" "^1.12.1"
    acorn "^8.7.1"
    acorn-import-assertions "^1.9.0"
    browserslist "^4.21.10"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.16.0"
    es-module-lexer "^1.2.1"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.11"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.2.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.3.10"
    watchpack "^2.4.1"
    webpack-sources "^3.2.3"

websocket-driver@>=0.5.1, websocket-driver@^0.7.4:
  version "0.7.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/websocket-driver/-/websocket-driver-0.7.4.tgz#89ad5295bbf64b480abcba31e4953aca706f5760"
  integrity sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/websocket-extensions/-/websocket-extensions-0.1.4.tgz#7f8473bc839dfd87608adb95d7eb075211578a42"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz#13757bc89b209b049fe5d86430e21cf40a89a8e6"
  integrity sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-typed-array@^1.1.14, which-typed-array@^1.1.15:
  version "1.1.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/which-typed-array/-/which-typed-array-1.1.15.tgz#264859e9b11a649b388bfaaf4f767df1f779b38d"
  integrity sha1-JkhZ6bEaZJs4i/qvT3Z98fd5s40=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.2"

which@^1.3.1:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/which/-/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/which/-/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wildcard@^2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/wildcard/-/wildcard-2.0.1.tgz#5ab10d02487198954836b6349f74fff961e10f67"
  integrity sha1-WrENAkhxmJVINrY0n3T/+WHhD2c=

wrappy@1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

ws@^8.13.0:
  version "8.17.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ws/-/ws-8.17.0.tgz#d145d18eca2ed25aaf791a183903f7be5e295fea"
  integrity sha1-0UXRjsou0lqveRoYOQP3vl4pX+o=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/yallist/-/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/yallist/-/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.10.0, yaml@^1.10.2, yaml@^1.7.2:
  version "1.10.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/yaml/-/yaml-1.10.2.tgz#2301c5ffbf12b467de8da2333a459e29e7920e4b"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/yocto-queue/-/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

yup@*, yup@^1.0.2:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/yup/-/yup-1.4.0.tgz#898dcd660f9fb97c41f181839d3d65c3ee15a43e"
  integrity sha1-iY3NZg+fuXxB8YGDnT1lw+4VpD4=
  dependencies:
    property-expr "^2.0.5"
    tiny-case "^1.0.3"
    toposort "^2.0.2"
    type-fest "^2.19.0"
