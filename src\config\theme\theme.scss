$primaryColor: #230563;
$secondaryColor: #952EFD;
$offColor: #ff9400;
$secondaryOffColor: #ff006d;
$shadowColor: rgba(0, 0, 0, 0.1);

$pageBgColor: #ffffff;
$primaryBgColor: rgba($primaryColor, 0.06);
$secondaryBgColor: #f3f3f3;

$grey: #395161;
$black: rgb(0, 0, 0, 0.87);
$red: #f44336;
$blue: #2196f3;
$green: #4caf50;
$yellow: #ffeb3b;
$orange: #ff9800;
$purple: #9c27b0;
$white: #fff;

$font: 'Poppins';
$fontColor: #707070;
$fontSize: 12px;
$letterSpacing: 0.8px;

// Tables
$headerLight: #230563;
$headerDark: rgba(0, 0, 0, 0.95);

:global {
  .arrow-right-dot {
    fill: $primaryColor;
  }
}

// export scss variables to TypeScript via Webpack
:export {
  primaryColor: $primaryColor;
  offColor: $offColor;
  secondaryColor: $secondaryColor;
  shadowColor: $shadowColor;
  secondaryOffColor: $secondaryOffColor;

  pageBgColor: $pageBgColor;
  primaryBgColor: $primaryBgColor;
  secondaryBgColor: $secondaryBgColor;

  grey: $grey;
  black: $black;
  red: $red;
  blue: $blue;
  green: $green;
  yellow: $yellow;
  orange: $orange;
  purple: $purple;
  white: $white;
  headerLight: $headerLight;
  headerDark: $headerDark;

  font: $font;
  fontSize: $fontSize;
  letterSpacing: $letterSpacing;
  fontColor: $fontColor;
}