import { AxisLabelsFormatterContextObject, AxisOptions, color, SeriesPieOptions, XAxisLabelsOptions } from "highcharts";
import { QueryResponseModel, ResponseField } from "query-redi-types";
import { useEffect, useState } from "react";
import { ChartConfigurationDto, FieldStandardOptions, WidgetDto, YAxisDto } from "redi-types";
import FieldFormatter from "sharedwidgetcomponents/FieldFormatter";
import { undefinedOrNullCheck } from "../utils/miscellaneousHelper";

interface YAxisOptions extends Highcharts.YAxisOptions {
    standardOptions?: FieldStandardOptions;
};

export type PlottedChartOptions = { 
    series: Highcharts.SeriesOptionsType[];
    xAxis: Highcharts.XAxisOptions | Highcharts.XAxisOptions[] | undefined;
    yAxis: YAxisOptions | YAxisOptions[] | undefined;
    type?: "column" | "line" | "scatter" | "pie" | "spline";
};

function transformYAxis(dto: YAxisDto, def?: YAxisDto) {
    const { fieldStandardOptions, ...rest } = dto;
    const { fieldStandardOptions: defFieldStandardOptions, ...defRest } = def ?? {};
    return {
        ...rest,
        ...defRest,
        title: { text: dto.title ?? "Values" },
        standardOptions: {
            ...fieldStandardOptions,
            ...defFieldStandardOptions
        }
    };
}

export const usePlottedChartOptions = (data: QueryResponseModel | undefined | null, widget: WidgetDto) => {

    const [ plottedOptions, setPlottedOptions ] = useState<PlottedChartOptions>();

    useEffect(() => {
        const configuration = widget.configuration.chart;
        let _series:Highcharts.SeriesOptionsType[] = [];
        let _xAxis:Highcharts.XAxisOptions | Highcharts.XAxisOptions[] | undefined = undefined;
        let _yAxis:YAxisOptions | YAxisOptions[] | undefined = configuration.options.yAxes ? configuration.options.yAxes.map(x => transformYAxis(x, configuration.options.yAxis)) : transformYAxis(configuration.options.yAxis);
        const xAxisOptions = configuration.options.xAxis;
        const hasYAxes = _yAxis instanceof Array;
        if (data && data.results) {
            let index = 0;
            for (const key in data.results) {
                const series = configuration.options.series.find(x => x.queryId ===  key);
                const response = data.results[key];
                if (series && response && response.values && response.fields) {
                    let isSeriesAsCategories = configuration.isSeriesAsCategories;
                    //Index of the categories in the values array response.
                    let categoryIndex = Math.max(0, response.fields.findIndex(x => x.name === series.categoryFieldName));
                    //Index of the data in the values array response. Can only plot number type.
                    let dataValuesIndex = categoryIndex === 0 ? 1 : 0;
                    let categories = response.values[categoryIndex];
                    //Categories does not exist, use series as fallback.
                    if (!undefinedOrNullCheck(response.values[dataValuesIndex])) {
                        dataValuesIndex = 0;
                        isSeriesAsCategories = true;
                    }
                    const chart = configuration.options.chart;
                    const options = configuration.options;
                    if (isSeriesAsCategories) {
                        if (_series.length === 0) {
                            _series.push({ id: widget.id, name: "Stats", index: index, data: [], type: chart.type!, custom: { seriesName: "value", dataField: response.fields[0], categoryField: response.fields[0]}, color: widget.styleProperties?.prominentColour });
                        }
                        const _pieSeries = _series[0] as SeriesPieOptions;
                        _pieSeries.data!.push([series.displayName, response.values[dataValuesIndex][0]]);
                        if (_xAxis === undefined || _xAxis === null) {
                            _xAxis = [];
                        }
                        if (Array.isArray(_xAxis) && _xAxis.length === 0) {
                            _xAxis?.push({ categories: options.series.map(x => x.displayName)});
                        }
                    } else {
                        const seriesColour = series.colour ?? widget.styleProperties?.prominentColour;
                        const values = response.values[dataValuesIndex];
                        const yAxisId = hasYAxes ? (series.yAxis ?? index) : undefined;
                        const yAxis = Array.isArray(_yAxis) ? _yAxis.find(x => x.id === yAxisId) : _yAxis;
                        if (yAxis) {
                            yAxis.labels = getAxisLabelOptions(response.fields[dataValuesIndex], widget, yAxis?.standardOptions);
                        }
                        const xAxisLabelOptions = getAxisLabelOptions(response.fields[categoryIndex], widget, xAxisOptions.fieldStandardOptions);
                        if (options.xAxis.isAxisShared) {
                            if (!_xAxis) {
                                _xAxis = { labels: xAxisLabelOptions, min: 0, categories: categories, id: response.fields[categoryIndex].name, title: { text: xAxisOptions.title ?? response.fields[categoryIndex].name } };
                            }
                            _series.push({ yAxis: yAxisId, name: series.displayName ?? "Series " + index, data: values, type: series.type ?? chart.type, custom: { seriesName: series.name, dataField: response.fields[dataValuesIndex], categoryField: response.fields[categoryIndex]}, color: seriesColour } as Highcharts.SeriesOptionsType);
                        } else if (!options.xAxis.isAxisShared) {
                            if (_xAxis === undefined || _xAxis === null) {
                                _xAxis = [];
                            }
                            if (Array.isArray(_xAxis)) {
                                _xAxis?.push({ labels: xAxisLabelOptions, min: 0, categories: categories, id: response.fields[categoryIndex].name, title: { text: xAxisOptions.title ?? response.fields[categoryIndex].name } });
                            }
                            _series.push({ yAxis: yAxisId, name: series.displayName ?? "Series " + index, data: values, index: index, type: series.type ?? chart.type, custom: { seriesName: series.name, dataField: response.fields[dataValuesIndex], categoryField: response.fields[categoryIndex] }, color: seriesColour } as Highcharts.SeriesOptionsType);
                        }
                        index++;
                    }
                }
            }
        }

        setPlottedOptions({
            series: _series,
            xAxis: _xAxis,
            yAxis: _yAxis,
            type: widget?.configuration?.chart?.options?.chart?.type
        });

    }, [data, widget?.configuration?.chart, widget?.fieldPropertiesOverrides]);

    return { plottedOptions };
};

function getAxisLabelOptions(field: ResponseField, widget: WidgetDto, fieldStandardOptions?: FieldStandardOptions): AxisOptions {
    const labelOptions = {} as XAxisLabelsOptions ;
    labelOptions.formatter = chartFormatter(field, widget, fieldStandardOptions);
    return labelOptions;
}

function chartFormatter(field: ResponseField, widget: WidgetDto, defaultFieldStandardOptions?: FieldStandardOptions) {
    return function(this: AxisLabelsFormatterContextObject) {
        return FieldFormatter.formatter("string", field, widget, this.value, defaultFieldStandardOptions);
    }
}


