import Chart from "../components/Chart/Chart";
import { WidgetDto } from "redi-types";
import { useEffect, useMemo, useRef, useState } from "react";
import { QueryResponseModel } from "query-redi-types";
import '../config/fa_icons';
import "./styles.scss";
import { <PERSON><PERSON>, Card, CardContent } from "@mui/material";
import Form from "../components/Form/Form";

const widget = {
  "id": "adroll-Predefined-1",
  "listingId": 101937,
  "description": "Combined Impressions, Clicks, and Cost",
  "caption": "Adroll",
  "title": "Combined Impressions, Click, Cost",
  "version": "1.0.0",
  "titlePositionCode": "Left",
  "titleColour": "red",
  "isTitleHidden": false,
  "refreshRate": 60000,
  "backgroundColour": "",
  "isFullScreen": false,
  "isFullScreenOptionEnabled": true,
  "widgetTypeCode": "chart",
  "widgetTypesAvailable": [
    "chart",
    "table",
    "hero",
    "stat"
  ],
  "configuration": {
    "chart": {
      "options": {
        "chart": {
          "title": "",
          "subtitle": "",
          "type": "column"
        },
        "xAxis": {
          "title": "Date",
          "type": "linear",
          "isAxisShared": false,
          "fieldStandardOptions": {
            "valueFormat": "DayMonth"
          }
        },
        "yAxis": {
          "type":"linear"
        },
        "yAxes": [{
          "title": "Impressions Count",
          "id": 0
        },{
          "title": "Click Count",
          "id": 1,
          "opposite": true
        },{
          "title": "Cost per Click",
          "id": 2,
          "fieldStandardOptions": {
            "formattedString": "${value}"
          }
        }],
        "legend": {
          "enabled": true,
          "align": "left",
          "margin": 0
        },
        "tooltip": {
          "enabled": true
        },
        "dataLabels": {
          "enabled": true,
          "align": "left"
        },
        "series": [
          {
            "name": "series_1",
            "displayName": "Impressions",
            "queryId": "query_1",
            "categoryFieldName": "Date",
            "yAxis": 0
          },
          {
            "name": "series_2",
            "displayName": "Clicks",
            "queryId": "query_2",
            "categoryFieldName": "Date",
            "yAxis": 1
          },
          {
            "name": "series_3",
            "displayName": "Cost per Click",
            "queryId": "query_3",
            "categoryFieldName": "Date",
            "yAxis": 2
          }
        ]
      },
      "isTableEnabled": false,
      "overrideOptions": [
        {
          "datasetGroupByOptions": [
            "grpBy-0"
          ],
          "xAxis": {
            "title": "Currency",
            "type": "linear",
            "isAxisShared": false,
            "fieldStandardOptions": {
              "valueFormat": "Text"
            }
          },
          "series": [
            {
              "name": "series_1",
              "displayName": "Impressions",
              "queryId": "query_1",
              "categoryFieldName": "Currency"
            },
            {
              "name": "series_2",
              "displayName": "Clicks",
              "queryId": "query_2",
              "categoryFieldName": "Currency"
            },
            {
              "name": "series_3",
              "displayName": "Cost per Click",
              "queryId": "query_3",
              "categoryFieldName": "Currency"
            }
          ]
        },
        {
          "datasetGroupByOptions": [
            "grpBy-1"
          ],
          "xAxis": {
            "title": "Advertising Platform",
            "type": "linear",
            "isAxisShared": false,
            "fieldStandardOptions": {
              "valueFormat": "Text"
            }
          },
          "series": [
            {
              "name": "series_1",
              "displayName": "Impressions",
              "queryId": "query_1",
              "categoryFieldName": "AdvertisingPlatformCode"
            },
            {
              "name": "series_2",
              "displayName": "Clicks",
              "queryId": "query_2",
              "categoryFieldName": "AdvertisingPlatformCode"
            },
            {
              "name": "series_3",
              "displayName": "Cost per Click",
              "queryId": "query_3",
              "categoryFieldName": "AdvertisingPlatformCode"
            }
          ]
        },
        {
          "datasetGroupByOptions": [
            "grpBy-2"
          ],
          "xAxis": {
            "title": "Ad Id",
            "type": "linear",
            "isAxisShared": false,
            "fieldStandardOptions": {
              "valueFormat": "Text"
            }
          },
          "series": [
            {
              "name": "series_1",
              "displayName": "Impressions",
              "queryId": "query_1",
              "categoryFieldName": "AdId"
            },
            {
              "name": "series_2",
              "displayName": "Clicks",
              "queryId": "query_2",
              "categoryFieldName": "AdId"
            },
            {
              "name": "series_3",
              "displayName": "Cost per Click",
              "queryId": "query_3",
              "categoryFieldName": "AdId"
            }
          ]
        },
        {
          "datasetGroupByOptions": [
            "grpBy-3"
          ],
          "xAxis": {
            "title": "Ad Name",
            "type": "linear",
            "isAxisShared": false,
            "fieldStandardOptions": {
              "valueFormat": "Text"
            }
          },
          "series": [
            {
              "name": "series_1",
              "displayName": "Impressions",
              "queryId": "query_1",
              "categoryFieldName": "AdName"
            },
            {
              "name": "series_2",
              "displayName": "Clicks",
              "queryId": "query_2",
              "categoryFieldName": "AdName"
            },
            {
              "name": "series_3",
              "displayName": "Cost per Click",
              "queryId": "query_3",
              "categoryFieldName": "AdName"
            }
          ]
        },
        {
          "datasetGroupByOptions": [
            "grpBy-4"
          ],
          "xAxis": {
            "title": "Campaign Id",
            "type": "linear",
            "isAxisShared": false,
            "fieldStandardOptions": {
              "valueFormat": "Text"
            }
          },
          "series": [
            {
              "name": "series_1",
              "displayName": "Impressions",
              "queryId": "query_1",
              "categoryFieldName": "CampaignId"
            },
            {
              "name": "series_2",
              "displayName": "Clicks",
              "queryId": "query_2",
              "categoryFieldName": "CampaignId"
            },
            {
              "name": "series_3",
              "displayName": "Cost per Click",
              "queryId": "query_3",
              "categoryFieldName": "CampaignId"
            }
          ]
        },
        {
          "datasetGroupByOptions": [
            "grpBy-5"
          ],
          "xAxis": {
            "title": "Campaign Name",
            "type": "linear",
            "isAxisShared": false,
            "fieldStandardOptions": {
              "valueFormat": "Text"
            }
          },
          "series": [
            {
              "name": "series_1",
              "displayName": "Impressions",
              "queryId": "query_1",
              "categoryFieldName": "CampaignName"
            },
            {
              "name": "series_2",
              "displayName": "Clicks",
              "queryId": "query_2",
              "categoryFieldName": "CampaignName"
            },
            {
              "name": "series_3",
              "displayName": "Cost per Click",
              "queryId": "query_3",
              "categoryFieldName": "CampaignName"
            }
          ]
        },
        {
          "datasetGroupByOptions": [
            "grpBy-6"
          ],
          "xAxis": {
            "title": "Keyword Id",
            "type": "linear",
            "isAxisShared": false,
            "fieldStandardOptions": {
              "valueFormat": "Text"
            }
          },
          "series": [
            {
              "name": "series_1",
              "displayName": "Impressions",
              "queryId": "query_1",
              "categoryFieldName": "KeywordId"
            },
            {
              "name": "series_2",
              "displayName": "Clicks",
              "queryId": "query_2",
              "categoryFieldName": "KeywordId"
            },
            {
              "name": "series_3",
              "displayName": "Cost per Click",
              "queryId": "query_3",
              "categoryFieldName": "KeywordId"
            }
          ]
        },
        {
          "datasetGroupByOptions": [
            "grpBy-7"
          ],
          "xAxis": {
            "title": "Keyword Name",
            "type": "linear",
            "isAxisShared": false,
            "fieldStandardOptions": {
              "valueFormat": "Text"
            }
          },
          "series": [
            {
              "name": "series_1",
              "displayName": "Impressions",
              "queryId": "query_1",
              "categoryFieldName": "KeywordName"
            },
            {
              "name": "series_2",
              "displayName": "Clicks",
              "queryId": "query_2",
              "categoryFieldName": "KeywordName"
            },
            {
              "name": "series_3",
              "displayName": "Cost per Click",
              "queryId": "query_3",
              "categoryFieldName": "KeywordName"
            }
          ]
        },
        {
          "datasetGroupByOptions": [
            "grpBy-8"
          ],
          "xAxis": {
            "title": "Day",
            "type": "linear",
            "isAxisShared": false,
            "fieldStandardOptions": {
              "valueFormat": "DayMonth"
            }
          },
          "series": [
            {
              "name": "series_1",
              "displayName": "Impressions",
              "queryId": "query_1",
              "categoryFieldName": "Date"
            },
            {
              "name": "series_2",
              "displayName": "Clicks",
              "queryId": "query_2",
              "categoryFieldName": "Date"
            },
            {
              "name": "series_3",
              "displayName": "Cost per Click",
              "queryId": "query_3",
              "categoryFieldName": "Date"
            }
          ]
        },
        {
          "datasetGroupByOptions": [
            "grpBy-9"
          ],
          "xAxis": {
            "title": "Week",
            "type": "linear",
            "isAxisShared": false,
            "fieldStandardOptions": {
              "valueFormat": "DayMonth"
            }
          },
          "series": [
            {
              "name": "series_1",
              "displayName": "Impressions",
              "queryId": "query_1",
              "categoryFieldName": "Date"
            },
            {
              "name": "series_2",
              "displayName": "Clicks",
              "queryId": "query_2",
              "categoryFieldName": "Date"
            },
            {
              "name": "series_3",
              "displayName": "Cost per Click",
              "queryId": "query_3",
              "categoryFieldName": "Date"
            }
          ]
        },
        {
          "datasetGroupByOptions": [
            "grpBy-10"
          ],
          "xAxis": {
            "title": "Month",
            "type": "linear",
            "isAxisShared": false,
            "fieldStandardOptions": {
              "valueFormat": "MonthFullNameYear"
            }
          },
          "series": [
            {
              "name": "series_1",
              "displayName": "Impressions",
              "queryId": "query_1",
              "categoryFieldName": "Date"
            },
            {
              "name": "series_2",
              "displayName": "Clicks",
              "queryId": "query_2",
              "categoryFieldName": "Date"
            },
            {
              "name": "series_3",
              "displayName": "Cost per Click",
              "queryId": "query_3",
              "categoryFieldName": "Date"
            }
          ]
        }
      ]
    },
    "table": {
      "options": {
        "pageSizeOptions": [
          10,
          25,
          50,
          100,
          200
        ],
        "defaultPageSize": 10,
        "columnOptions": {
          "sortable": true,
          "width": 150
        }
      }
    }
  },
  "queryRequests": {
    "general": {
      "dateRange": "Last30Days",
      "connector": "adroll",
      "dataset": "Advertising",
      "returnDbQueryString": true
    },
    "stat": {
      "queries": [
        {
          "queryId": "query_1",
          "filters": [
            
          ],
          "fields": [
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "interval": "DAY00001",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        },
        {
          "queryId": "query_1#previousperiod",
          "filters": [
            
          ],
          "fields": [
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "interval": "DAY00001",
          "previousPeriod": "PreviousPeriod",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        }
      ]
    },
    "hero": {
      "queries": [
        {
          "queryId": "query_1",
          "filters": [
            
          ],
          "fields": [
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "interval": "DAY00001",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        },
        {
          "queryId": "query_1#previousperiod",
          "filters": [
            
          ],
          "fields": [
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "interval": "DAY00001",
          "previousPeriod": "PreviousPeriod",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        }
      ]
    },
    "chart": {
      "queries": [
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-0"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Currency",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "Currency",
          "interval": "DAY00001",
          "orderBy": "Currency",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Currency,Date"
        },
        {
          "queryId": "query_2",
          "datasetGroupByOptions": [
            "grpBy-0"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Currency",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "Currency",
          "interval": "DAY00001",
          "orderBy": "Currency",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Currency,Date"
        },
        {
          "queryId": "query_3",
          "datasetGroupByOptions": [
            "grpBy-0"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Currency",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": false,
          "summariseBy": "Currency",
          "interval": "DAY00001",
          "orderBy": "Currency",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Currency,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-1"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "AdvertisingPlatformCode",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "AdvertisingPlatformCode",
          "interval": "DAY00001",
          "orderBy": "AdvertisingPlatformCode",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "AdvertisingPlatformCode,Date"
        },
        {
          "queryId": "query_2",
          "datasetGroupByOptions": [
            "grpBy-1"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "AdvertisingPlatformCode",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "AdvertisingPlatformCode",
          "interval": "DAY00001",
          "orderBy": "AdvertisingPlatformCode",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "AdvertisingPlatformCode,Date"
        },
        {
          "queryId": "query_3",
          "datasetGroupByOptions": [
            "grpBy-1"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "AdvertisingPlatformCode",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": false,
          "summariseBy": "AdvertisingPlatformCode",
          "interval": "DAY00001",
          "orderBy": "AdvertisingPlatformCode",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "AdvertisingPlatformCode,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-2"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "AdId",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "AdId",
          "interval": "DAY00001",
          "orderBy": "AdId",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "AdId,Date"
        },
        {
          "queryId": "query_2",
          "datasetGroupByOptions": [
            "grpBy-2"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "AdId",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "AdId",
          "interval": "DAY00001",
          "orderBy": "AdId",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "AdId,Date"
        },
        {
          "queryId": "query_3",
          "datasetGroupByOptions": [
            "grpBy-2"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "AdId",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": false,
          "summariseBy": "AdId",
          "interval": "DAY00001",
          "orderBy": "AdId",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "AdId,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-3"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "AdName",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "AdName",
          "interval": "DAY00001",
          "orderBy": "AdName",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "AdName,Date"
        },
        {
          "queryId": "query_2",
          "datasetGroupByOptions": [
            "grpBy-3"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "AdName",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "AdName",
          "interval": "DAY00001",
          "orderBy": "AdName",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "AdName,Date"
        },
        {
          "queryId": "query_3",
          "datasetGroupByOptions": [
            "grpBy-3"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "AdName",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": false,
          "summariseBy": "AdName",
          "interval": "DAY00001",
          "orderBy": "AdName",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "AdName,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-4"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "CampaignId",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "CampaignId",
          "interval": "DAY00001",
          "orderBy": "CampaignId",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "CampaignId,Date"
        },
        {
          "queryId": "query_2",
          "datasetGroupByOptions": [
            "grpBy-4"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "CampaignId",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "CampaignId",
          "interval": "DAY00001",
          "orderBy": "CampaignId",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "CampaignId,Date"
        },
        {
          "queryId": "query_3",
          "datasetGroupByOptions": [
            "grpBy-4"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "CampaignId",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": false,
          "summariseBy": "CampaignId",
          "interval": "DAY00001",
          "orderBy": "CampaignId",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "CampaignId,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-5"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "CampaignName",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "CampaignName",
          "interval": "DAY00001",
          "orderBy": "CampaignName",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "CampaignName,Date"
        },
        {
          "queryId": "query_2",
          "datasetGroupByOptions": [
            "grpBy-5"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "CampaignName",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "CampaignName",
          "interval": "DAY00001",
          "orderBy": "CampaignName",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "CampaignName,Date"
        },
        {
          "queryId": "query_3",
          "datasetGroupByOptions": [
            "grpBy-5"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "CampaignName",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": false,
          "summariseBy": "CampaignName",
          "interval": "DAY00001",
          "orderBy": "CampaignName",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "CampaignName,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-6"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "KeywordId",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "KeywordId",
          "interval": "DAY00001",
          "orderBy": "KeywordId",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "KeywordId,Date"
        },
        {
          "queryId": "query_2",
          "datasetGroupByOptions": [
            "grpBy-6"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "KeywordId",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "KeywordId",
          "interval": "DAY00001",
          "orderBy": "KeywordId",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "KeywordId,Date"
        },
        {
          "queryId": "query_3",
          "datasetGroupByOptions": [
            "grpBy-6"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "KeywordId",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": false,
          "summariseBy": "KeywordId",
          "interval": "DAY00001",
          "orderBy": "KeywordId",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "KeywordId,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-7"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "KeywordName",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "KeywordName",
          "interval": "DAY00001",
          "orderBy": "KeywordName",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "KeywordName,Date"
        },
        {
          "queryId": "query_2",
          "datasetGroupByOptions": [
            "grpBy-7"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "KeywordName",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "KeywordName",
          "interval": "DAY00001",
          "orderBy": "KeywordName",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "KeywordName,Date"
        },
        {
          "queryId": "query_3",
          "datasetGroupByOptions": [
            "grpBy-7"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "KeywordName",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": false,
          "summariseBy": "KeywordName",
          "interval": "DAY00001",
          "orderBy": "KeywordName",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "KeywordName,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-8"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Date",
              "typeCode": "date"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "Date",
          "interval": "DAY00001",
          "orderBy": "Date",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        },
        {
          "queryId": "query_2",
          "datasetGroupByOptions": [
            "grpBy-8"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Date",
              "typeCode": "date"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "Date",
          "interval": "DAY00001",
          "orderBy": "Date",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        },
        {
          "queryId": "query_3",
          "datasetGroupByOptions": [
            "grpBy-8"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Date",
              "typeCode": "date"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": false,
          "summariseBy": "Date",
          "interval": "DAY00001",
          "orderBy": "Date",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-9"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Date",
              "typeCode": "date"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "Date",
          "interval": "WEK01",
          "orderBy": "Date",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        },
        {
          "queryId": "query_2",
          "datasetGroupByOptions": [
            "grpBy-9"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Date",
              "typeCode": "date"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "Date",
          "interval": "WEK01",
          "orderBy": "Date",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        },
        {
          "queryId": "query_3",
          "datasetGroupByOptions": [
            "grpBy-9"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Date",
              "typeCode": "date"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": false,
          "summariseBy": "Date",
          "interval": "WEK01",
          "orderBy": "Date",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-10"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Date",
              "typeCode": "date"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "Date",
          "interval": "MTH01",
          "orderBy": "Date",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        },
        {
          "queryId": "query_2",
          "datasetGroupByOptions": [
            "grpBy-10"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Date",
              "typeCode": "date"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            }
          ],
          "isTableData": false,
          "summariseBy": "Date",
          "interval": "MTH01",
          "orderBy": "Date",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        },
        {
          "queryId": "query_3",
          "datasetGroupByOptions": [
            "grpBy-10"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Date",
              "typeCode": "date"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": false,
          "summariseBy": "Date",
          "interval": "MTH01",
          "orderBy": "Date",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        }
      ]
    },
    "table": {
      "queries": [
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-0"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Currency",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "summariseBy": "Currency",
          "interval": "DAY00001",
          "orderBy": "Currency",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Currency,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-1"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "AdvertisingPlatformCode",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "summariseBy": "AdvertisingPlatformCode",
          "interval": "DAY00001",
          "orderBy": "AdvertisingPlatformCode",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "AdvertisingPlatformCode,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-2"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "AdId",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "summariseBy": "AdId",
          "interval": "DAY00001",
          "orderBy": "AdId",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "AdId,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-3"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "AdName",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "summariseBy": "AdName",
          "interval": "DAY00001",
          "orderBy": "AdName",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "AdName,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-4"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "CampaignId",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "summariseBy": "CampaignId",
          "interval": "DAY00001",
          "orderBy": "CampaignId",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "CampaignId,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-5"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "CampaignName",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "summariseBy": "CampaignName",
          "interval": "DAY00001",
          "orderBy": "CampaignName",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "CampaignName,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-6"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "KeywordId",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "summariseBy": "KeywordId",
          "interval": "DAY00001",
          "orderBy": "KeywordId",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "KeywordId,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-7"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "KeywordName",
              "typeCode": "string"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "summariseBy": "KeywordName",
          "interval": "DAY00001",
          "orderBy": "KeywordName",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "KeywordName,Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-8"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Date",
              "typeCode": "date"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "summariseBy": "Date",
          "interval": "DAY00001",
          "orderBy": "Date",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-9"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Date",
              "typeCode": "date"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "summariseBy": "Date",
          "interval": "WEK01",
          "orderBy": "Date",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        },
        {
          "queryId": "query_1",
          "datasetGroupByOptions": [
            "grpBy-10"
          ],
          "filters": [
            
          ],
          "fields": [
            {
              "name": "Date",
              "typeCode": "date"
            },
            {
              "name": "ImpressionsCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "TotalClicksCount",
              "aggregateFunction": "sum",
              "typeCode": "string"
            },
            {
              "name": "CostPerClickMoney",
              "aggregateFunction": "avg",
              "typeCode": "decimal"
            }
          ],
          "isTableData": true,
          "summariseBy": "Date",
          "interval": "MTH01",
          "orderBy": "Date",
          "enforceUniqueRows": true,
          "enforceUniqueRowsBy": "Date"
        }
      ]
    }
  },
  "queryHelperFields": {
    "datasetGroupBy": {
      "code": "grpBy-8",
      "label": "Day",
      "datasetGroupBy": "Ad_ByDay"
    },
    "datasetGroupByOptions": [
      {
        "code": "grpBy-2",
        "label": "Ad Id",
        "datasetGroupBy": "Ad_ByDay",
        "filterOptions": [
          {
            "code": "AdId",
            "label": "Ad Id",
            "datasetGroupBy": "Ad_ByDay"
          }
        ]
      },
      {
        "code": "grpBy-3",
        "label": "Ad Name",
        "datasetGroupBy": "Ad_ByDay",
        "filterOptions": [
          {
            "code": "AdName",
            "label": "Ad Name",
            "datasetGroupBy": "Ad_ByDay"
          }
        ]
      },
      {
        "code": "grpBy-1",
        "label": "Advertising Platform",
        "datasetGroupBy": "Ad_ByDay",
        "filterOptions": [
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Summary_ByDay"
          }
        ]
      },
      {
        "code": "grpBy-4",
        "label": "Campaign Id",
        "datasetGroupBy": "Campaign_ByDay",
        "filterOptions": [
          {
            "code": "CampaignId",
            "label": "Campaign Id",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "CampaignId",
            "label": "Campaign Id",
            "datasetGroupBy": "Keyword_ByDay"
          }
        ]
      },
      {
        "code": "grpBy-5",
        "label": "Campaign Name",
        "datasetGroupBy": "Campaign_ByDay",
        "filterOptions": [
          {
            "code": "CampaignName",
            "label": "Campaign Name",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "CampaignName",
            "label": "Campaign Name",
            "datasetGroupBy": "Keyword_ByDay"
          }
        ]
      },
      {
        "code": "grpBy-0",
        "label": "Currency",
        "datasetGroupBy": "Ad_ByDay",
        "filterOptions": [
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Summary_ByDay"
          }
        ]
      },
      {
        "code": "grpBy-8",
        "label": "Day",
        "datasetGroupBy": "Ad_ByDay",
        "filterOptions": [
          {
            "code": "AdId",
            "label": "Ad Id",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "AdName",
            "label": "Ad Name",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Summary_ByDay"
          },
          {
            "code": "CampaignId",
            "label": "Campaign Id",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "CampaignId",
            "label": "Campaign Id",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "CampaignName",
            "label": "Campaign Name",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "CampaignName",
            "label": "Campaign Name",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Summary_ByDay"
          },
          {
            "code": "KeywordId",
            "label": "Keyword Id",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "KeywordName",
            "label": "Keyword Name",
            "datasetGroupBy": "Keyword_ByDay"
          }
        ]
      },
      {
        "code": "grpBy-6",
        "label": "Keyword Id",
        "datasetGroupBy": "Keyword_ByDay",
        "filterOptions": [
          {
            "code": "KeywordId",
            "label": "Keyword Id",
            "datasetGroupBy": "Keyword_ByDay"
          }
        ]
      },
      {
        "code": "grpBy-7",
        "label": "Keyword Name",
        "datasetGroupBy": "Keyword_ByDay",
        "filterOptions": [
          {
            "code": "KeywordName",
            "label": "Keyword Name",
            "datasetGroupBy": "Keyword_ByDay"
          }
        ]
      },
      {
        "code": "grpBy-10",
        "label": "Month",
        "datasetGroupBy": "Ad_ByDay",
        "filterOptions": [
          {
            "code": "AdId",
            "label": "Ad Id",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "AdName",
            "label": "Ad Name",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Summary_ByDay"
          },
          {
            "code": "CampaignId",
            "label": "Campaign Id",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "CampaignId",
            "label": "Campaign Id",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "CampaignName",
            "label": "Campaign Name",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "CampaignName",
            "label": "Campaign Name",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Summary_ByDay"
          },
          {
            "code": "KeywordId",
            "label": "Keyword Id",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "KeywordName",
            "label": "Keyword Name",
            "datasetGroupBy": "Keyword_ByDay"
          }
        ]
      },
      {
        "code": "grpBy-9",
        "label": "Week",
        "datasetGroupBy": "Ad_ByDay",
        "filterOptions": [
          {
            "code": "AdId",
            "label": "Ad Id",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "AdName",
            "label": "Ad Name",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "AdvertisingPlatformCode",
            "label": "Advertising Platform",
            "datasetGroupBy": "Summary_ByDay"
          },
          {
            "code": "CampaignId",
            "label": "Campaign Id",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "CampaignId",
            "label": "Campaign Id",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "CampaignName",
            "label": "Campaign Name",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "CampaignName",
            "label": "Campaign Name",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Ad_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Campaign_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "Currency",
            "label": "Currency",
            "datasetGroupBy": "Summary_ByDay"
          },
          {
            "code": "KeywordId",
            "label": "Keyword Id",
            "datasetGroupBy": "Keyword_ByDay"
          },
          {
            "code": "KeywordName",
            "label": "Keyword Name",
            "datasetGroupBy": "Keyword_ByDay"
          }
        ]
      }
    ]
  },
  "fieldPropertiesOverrides": [
    {
      "id": "8594d439-9698-4ed2-8234-66986d7a91cc",
      "fieldName": "ImpressionsCount",
      "properties": {
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "a1e96315-2506-484a-a523-60851b0767c3",
      "fieldName": "metric",
      "properties": {
        "tableColumnOptions": {
          "width": 300
        }
      }
    },
    {
      "id": "15edc328-fe62-4579-b5ad-a91bbfe620b6",
      "fieldName": "Date",
      "properties": {
        "fieldStandardOptions": {
          "valueFormat": "DayMonth",
          "displayName": "Date"
        },
        "tableColumnOptions": {
          "displayName": "Date",
          "width": 200
        }
      }
    },
    {
      "id": "26c88a4a-a9a8-464e-a291-2412f0e2fae3",
      "fieldName": "sumImpressionsCount",
      "properties": {
        "fieldStandardOptions": {
          "valueFormat": "NoDecimal",
          "displayName": "Impressions"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "7c733212-06f9-4ee9-83ca-c92af97d29d7",
      "fieldName": "sumImpressionsCount",
      "properties": {
        "fieldStandardOptions": {
          "valueFormat": "NoDecimal",
          "displayName": "Sum Impressions"
        },
        "tableColumnOptions": {
          "width": 200
        }
      },
      "previousPeriodProperties": {
        "isPositiveValueArrowUp": true,
        "fieldStandardOptions": {
          "formattedString": "{value}"
        }
      }
    },
    {
      "id": "3362969b-b384-4190-85fa-38923f18c88d",
      "fieldName": "sumImpressionsCount",
      "properties": {
        "fieldStandardOptions": {
          "valueFormat": "NoDecimal",
          "displayName": "Sum Impressions"
        },
        "tableColumnOptions": {
          "width": 200
        }
      },
      "previousPeriodProperties": {
        "isPositiveValueArrowUp": true,
        "fieldStandardOptions": {
          "formattedString": "{value}"
        }
      }
    },
    {
      "id": "0a3cdec6-3316-44ca-af9b-e7828f63a2ed",
      "fieldName": "TotalClicksCount",
      "properties": {
        "fieldStandardOptions": {
          "valueFormat": "NoDecimal",
          "displayName": "Clicks"
        },
        "tableColumnOptions": {
          "width": 150
        }
      }
    },
    {
      "id": "00f504b2-922b-4c78-bced-41e2ba461e65",
      "fieldName": "sumTotalClicksCount",
      "properties": {
        "fieldStandardOptions": {
          "valueFormat": "NoDecimal",
          "displayName": "Clicks"
        },
        "tableColumnOptions": {
          "width": 150
        }
      }
    },
    {
      "id": "b763d01c-6234-45c0-b2ad-75bb6ba3c266",
      "fieldName": "CostPerClickMoney",
      "properties": {
        "fieldStandardOptions": {
          "valueFormat": "TwoDecimal",
          "displayName": "Cost per Click"
        },
        "tableColumnOptions": {
          "width": 150
        }
      }
    },
    {
      "id": "b3162f83-fb48-4096-a7a5-7bdf0546a63e",
      "fieldName": "avgCostPerClickMoney",
      "properties": {
        "fieldStandardOptions": {
          "valueFormat": "TwoDecimal",
          "displayName": "Cost per Click"
        },
        "tableColumnOptions": {
          "width": 150
        }
      }
    },
    {
      "id": "c30690db-5046-419b-82f9-9ac12cebe0ee",
      "fieldName": "Currency",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Currency"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "e63d52b0-a3f6-4345-8790-cc124beed0f1",
      "fieldName": "AdvertisingPlatformCode",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Advertising Platform"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "9189aba0-83d4-4492-a39e-6b017cdc58d7",
      "fieldName": "AdId",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Ad Id"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "771b3c6b-1404-4605-8816-375233a67d03",
      "fieldName": "AdName",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Ad Name"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "d3f27246-dd1a-41ed-8603-a2761dc01eec",
      "fieldName": "Currency",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Currency"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "ea6fa37b-9a05-4d7c-ab8a-891bd3caa433",
      "fieldName": "AdvertisingPlatformCode",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Advertising Platform"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "dd75ec3a-6ee1-4c18-bcff-0cf6f2157f68",
      "fieldName": "CampaignId",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Campaign Id"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "1e177328-1a59-414a-aa4e-1a945a29c885",
      "fieldName": "CampaignName",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Campaign Name"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "c4c6d1b4-1edb-480a-af39-6005e314fb1c",
      "fieldName": "Currency",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Currency"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "2af4b2be-785d-4b7f-be30-5fb7673f3291",
      "fieldName": "AdvertisingPlatformCode",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Advertising Platform"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "574b2e26-0e05-461c-b967-ff3b004a7104",
      "fieldName": "CampaignId",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Campaign Id"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "6faf805f-fc8e-4b88-83cb-a7e97096bd17",
      "fieldName": "CampaignName",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Campaign Name"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "fc640a48-6d35-4d34-8bfe-8ff1ebee2305",
      "fieldName": "KeywordId",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Keyword Id"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "53bdefdb-0d34-4920-ac87-e78fcbfc80d4",
      "fieldName": "KeywordName",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Keyword Name"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "85c6b68d-b54e-4c16-8cc9-16ba36d8d63f",
      "fieldName": "Currency",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Currency"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    },
    {
      "id": "80f51af4-6c65-421c-8a82-557304e8e46b",
      "fieldName": "AdvertisingPlatformCode",
      "properties": {
        "fieldStandardOptions": {
          "displayName": "Advertising Platform"
        },
        "tableColumnOptions": {
          "width": 200
        }
      }
    }
  ],
  "isDraftModeEnabled": false
} as any;
// const widget = {
//   "id": "chart-zendesk-priority-totals",
//   "description": "Zendesk Priority Totals Chart",
//   "listingId": 147,
//   "caption": "",
//   "title": "Tickets Priority Totals",
//   "titlePositionCode": "left",
//   "titleColour": "red",
//   "isTitleHidden": false,
//   "refreshRate": 60000,
//   "backgroundColour": "",
//   "isFullScreen": false,
//   "isFullScreenOptionEnabled": false,
//   "widgetTypeCode": "chart",
//   "widgetTypesAvailable": [ "chart", "table" ],
//   "isDraftModeEnabled": false,
//   "queryRequests": {
//     "general": {
//       "returnDbQueryString": true,
//       "dateRange": "Last30Days",
//       "connector": "Zendesk",
//       "dataset": "CustomerSupport",
//       "queries": [],
//     },
//     "table": {
//       "queries": [
//         {
//           "isTableData": true,
//           "queryId": "query-id",
//           "datasetGroupBy": "TicketPriority_ByDay",
//           "filters": [],
//           "fields": [
//             {
//               "name": "PriorityName",
//               "typeCode": "string"
//             },
//             {
//               "name": "OpenTicketsCount",
//               "typeCode": "number",
//               "aggregateFunction": "max"
//             }
//           ],
//           "summariseBy": "PriorityName",
//           "orderBy": "PriorityName",
//           "enforceUniqueRows": true,
//           "enforceUniqueRowsBy": "PriorityName",
//           "interval": "DAY0001"
//         }
//       ]
//     },
//     "chart": {
//       "queries": [
//         {
//           "isTableData": false,
//           "queryId": "query-id-1",
//           "datasetGroupBy": "TicketPriority_ByDay",
//           "filters": [
//             "PriorityName == ''urgent''"
//           ],
//           "fields": [
//             {
//               "name": "OpenTicketsCount",
//               "typeCode": "number",
//               "aggregateFunction": "max"
//             }
//           ],
//           "enforceUniqueRows": true,
//           "enforceUniqueRowsBy": "Date",
//           "interval": "DAY0001"
//         },
//         {
//           "isTableData": false,
//           "queryId": "query-id-2",
//           "datasetGroupBy": "TicketPriority_ByDay",
//           "filters": [
//             "PriorityName == ''high''"
//           ],
//           "fields": [
//             {
//               "name": "OpenTicketsCount",
//               "typeCode": "number",
//               "aggregateFunction": "max"
//             }
//           ],
//           "enforceUniqueRows": true,
//           "enforceUniqueRowsBy": "Date",
//           "interval": "DAY0001"
//         },
//         {
//           "isTableData": false,
//           "queryId": "query-id-3",
//           "datasetGroupBy": "TicketPriority_ByDay",
//           "filters": [
//             "PriorityName == ''normal''"
//           ],
//           "fields": [
//             {
//               "name": "OpenTicketsCount",
//               "typeCode": "number",
//               "aggregateFunction": "max"
//             }
//           ],
//           "enforceUniqueRows": true,
//           "enforceUniqueRowsBy": "Date",
//           "interval": "DAY0001"
//         },
//         {
//           "isTableData": false,
//           "queryId": "query-id-4",
//           "datasetGroupBy": "TicketPriority_ByDay",
//           "filters": [
//             "PriorityName == ''''"
//           ],
//           "fields": [
//             {
//               "name": "OpenTicketsCount",
//               "typeCode": "number",
//               "aggregateFunction": "max"
//             }
//           ],
//           "enforceUniqueRows": true,
//           "enforceUniqueRowsBy": "Date",
//           "interval": "DAY0001"
//         }
//       ]
//     }
//   },
//   "fieldPropertiesOverrides": [{
//     "id": "d022b510-4185-4446-a6a6-dfe1b4e577f0",
//     "fieldName": "PriorityName",
//     "properties": {
//       "fieldStandardOptions": {
//         "valueFormat": "Text"
//       },
//       "tableCellOptions": {},
//       "tableColumnOptions": {
//         "displayName": "Priority",
//         "width": 200
//       }
//     }
//   }
//   ],
//   "queryHelperFields": {
//     "datasetGroupByOptions": [
//       { 
//         "code": "Summary_ByDay", 
//         "label": "Summary By Day"
//       },
//       {
//         "code": "TicketPriority_ByDay", 
//         "label": "Ticket Priority"
//       }
//     ],
//     "datasetGroupBy": { 
//         "code": "TicketPriority_ByDay", 
//         "label": "Ticket Priority"
//     },
//     "filterOptions": [
//       {
//         "code": "PriorityName", 
//         "label": "Priority Name"
//       },
//       {
//         "code": "CompanyName", 
//         "label": "Company Name"
//       }
//     ]
//   },
//   "configuration": {
//     "table": {
//       "options": {
//         "title": "Table",
//         "pageSizeOptions": [
//           5,
//           25,
//           100
//         ],
//         "defaultPageSize": 25,
//         "columnOptions": {
//           "width": 150
//         }
//       }
//     },
//     "chart": {
//       "isTableEnabled": true,
//       "isSeriesAsCategories": true,
//       "options": {
//         "series": [
//           {
//             "name": "series_1_urgent",
//             "displayName": "Urgent",
//             "queryId": "query-id-1",
//             "categoryFieldName": "maxOpenTicketsCount"
//           },
//           {
//             "name": "series_2_high",
//             "displayName": "High",
//             "queryId": "query-id-2",
//             "categoryFieldName": "maxOpenTicketsCount2"
//           },
//           {
//             "name": "series_3_pending",
//             "displayName": "Normal",
//             "queryId": "query-id-3",
//             "categoryFieldName": "maxOpenTicketsCount3"
//           },
//           {
//             "name": "series_4_onhold",
//             "displayName": "Not Set",
//             "queryId": "query-id-4",
//             "categoryFieldName": "maxOpenTicketsCount4"
//           }
//         ],
//         "chart": {
//           "title": "Ticket Priority",
//           "type": "pie",
//           "subtitle": ""
//         },
//         "xAxis": {
//           "title": "Date",
//           "type": "linear",
//           "isAxisShared": false,
//           "fieldStandardOptions": {
//             "valueFormat": "Text",
//             "formattedString": "{value}"
//           }
//         },
//         "yAxis": {
//           "title": "Count",
//           "type": "linear"
//         },
//         "legend": {
//           "enabled": true,
//           "align": "left",
//           "margin": 0
//         },
//         "tooltip": {
//           "enabled": true
//         },
//         "dataLabels": {
//           "enabled": true,
//           "align": "left"
//         }
//       }
//     }
//   }
// } as any;

// const data = {
//   "results": {
//       "query-id-1": {
//           "statusCode": 200,
//           "values": [
//               [
//                   2
//               ]
//           ],
//           "fields": [
//               {
//                   "name": "maxOpenTicketsCount",
//                   "displayName": "max OpenTicketsCount",
//                   "typeCode": "number"
//               }
//           ],
//           "queryId": "query-id-1",
//           "fromDate": "2024-09-04T11:29:20.4737822+08:00",
//           "toDate": "2024-10-04T11:29:20.4737822+08:00",
//           "databaseQueryString": "\n                CustomerSupport_TicketPriority_ByDay_zendesk\n             | where  Date >= datetime('09/04/2024 11:29:20 +08:00') and  Date <= datetime('10/04/2024 11:29:20 +08:00') | where PriorityName == 'urgent'| summarize arg_max(PDEnqueuedOn, *) by Date  | summarize maxOpenTicketsCount = max(OpenTicketsCount)  | project  maxOpenTicketsCount| take 1000"
//       },
//       "query-id-2": {
//           "statusCode": 200,
//           "values": [
//               [
//                   12
//               ]
//           ],
//           "fields": [
//               {
//                   "name": "maxOpenTicketsCount",
//                   "displayName": "max OpenTicketsCount",
//                   "typeCode": "number"
//               }
//           ],
//           "queryId": "query-id-2",
//           "fromDate": "2024-09-04T11:29:20.4737822+08:00",
//           "toDate": "2024-10-04T11:29:20.4737822+08:00",
//           "databaseQueryString": "\n                CustomerSupport_TicketPriority_ByDay_zendesk\n             | where  Date >= datetime('09/04/2024 11:29:20 +08:00') and  Date <= datetime('10/04/2024 11:29:20 +08:00') | where PriorityName == 'high'| summarize arg_max(PDEnqueuedOn, *) by Date  | summarize maxOpenTicketsCount = max(OpenTicketsCount)  | project  maxOpenTicketsCount| take 1000"
//       },
//       "query-id-3": {
//           "statusCode": 200,
//           "values": [
//               [
//                   25
//               ]
//           ],
//           "fields": [
//               {
//                   "name": "maxOpenTicketsCount",
//                   "displayName": "max OpenTicketsCount",
//                   "typeCode": "number"
//               }
//           ],
//           "queryId": "query-id-3",
//           "fromDate": "2024-09-04T11:29:20.4737822+08:00",
//           "toDate": "2024-10-04T11:29:20.4737822+08:00",
//           "databaseQueryString": "\n                CustomerSupport_TicketPriority_ByDay_zendesk\n             | where  Date >= datetime('09/04/2024 11:29:20 +08:00') and  Date <= datetime('10/04/2024 11:29:20 +08:00') | where PriorityName == 'normal'| summarize arg_max(PDEnqueuedOn, *) by Date  | summarize maxOpenTicketsCount = max(OpenTicketsCount)  | project  maxOpenTicketsCount| take 1000"
//       },
//       "query-id-4": {
//           "statusCode": 200,
//           "values": [
//               [
//                   9
//               ]
//           ],
//           "fields": [
//               {
//                   "name": "maxOpenTicketsCount",
//                   "displayName": "max OpenTicketsCount",
//                   "typeCode": "number"
//               }
//           ],
//           "queryId": "query-id-4",
//           "fromDate": "2024-09-04T11:29:20.4737822+08:00",
//           "toDate": "2024-10-04T11:29:20.4737822+08:00",
//           "databaseQueryString": "\n                CustomerSupport_TicketPriority_ByDay_zendesk\n             | where  Date >= datetime('09/04/2024 11:29:20 +08:00') and  Date <= datetime('10/04/2024 11:29:20 +08:00') | where PriorityName == ''| summarize arg_max(PDEnqueuedOn, *) by Date  | summarize maxOpenTicketsCount = max(OpenTicketsCount)  | project  maxOpenTicketsCount| take 1000"
//       }
//   },
//   "fromDate": "2024-09-04T11:29:20.4737822+08:00",
//   "toDate": "2024-10-04T11:29:20.4737822+08:00",
//   "ianaTimeZone": "Australia/Perth",
//   "startOfWeek": "Sunday",
//   "financialYearStart": "1/1"
// };

//const fakerService = new ServiceFaker(widget);


const data = {
  "results": {
      "query_1": {
          "statusCode": 200,
          "values": [
              [
                  "2025-04-06T00:00:00Z",
                  "2025-04-07T00:00:00Z",
                  "2025-04-08T00:00:00Z",
                  "2025-04-09T00:00:00Z",
                  "2025-04-10T00:00:00Z",
                  "2025-04-11T00:00:00Z",
                  "2025-04-12T00:00:00Z",
                  "2025-04-13T00:00:00Z"
              ],
              [
                  12398,
                  12287,
                  1258,
                  12610,
                  12426,
                  12521,
                  12348,
                  12303
              ]
          ],
          "fields": [
              {
                  "name": "Date",
                  "displayName": "Date",
                  "typeCode": "date"
              },
              {
                  "name": "sumImpressionsCount",
                  "displayName": "sum ImpressionsCount",
                  "typeCode": "string"
              }
          ],
          "queryId": "query_1",
          "fromDate": "2025-03-15T10:55:24.9058533+08:00",
          "toDate": "2025-04-14T10:55:24.9058533+08:00",
          "databaseQueryString": "\n                Advertising_Ad_ByDay_adroll\n             | where  Date >= datetime('03/15/2025 10:55:24 +08:00') and  Date <= datetime('04/14/2025 10:55:24 +08:00')| summarize arg_max(PDEnqueuedOn, *) by Date  | summarize sumImpressionsCount = sum(ImpressionsCount)  by bin(Date, 1d)    | order by  Date asc  | project  Date, sumImpressionsCount| take 1000",
          "isDynamicResult": false
      },
      "query_2": {
          "statusCode": 200,
          "values": [
              [
                  "2025-04-06T00:00:00Z",
                  "2025-04-07T00:00:00Z",
                  "2025-04-08T00:00:00Z",
                  "2025-04-09T00:00:00Z",
                  "2025-04-10T00:00:00Z",
                  "2025-04-11T00:00:00Z",
                  "2025-04-12T00:00:00Z",
                  "2025-04-13T00:00:00Z"
              ],
              [
                  31112,
                  78880,
                  123210,
                  123211,
                  543540,
                  453451,
                  543562,
                  234441
              ]
          ],
          "fields": [
              {
                  "name": "Date",
                  "displayName": "Date",
                  "typeCode": "date"
              },
              {
                  "name": "sumTotalClicksCount",
                  "displayName": "sum TotalClicksCount",
                  "typeCode": "string"
              }
          ],
          "queryId": "query_2",
          "fromDate": "2025-03-15T10:55:24.9058533+08:00",
          "toDate": "2025-04-14T10:55:24.9058533+08:00",
          "databaseQueryString": "\n                Advertising_Ad_ByDay_adroll\n             | where  Date >= datetime('03/15/2025 10:55:24 +08:00') and  Date <= datetime('04/14/2025 10:55:24 +08:00')| summarize arg_max(PDEnqueuedOn, *) by Date  | summarize sumTotalClicksCount = sum(TotalClicksCount)  by bin(Date, 1d)    | order by  Date asc  | project  Date, sumTotalClicksCount| take 1000",
          "isDynamicResult": false
      },
      "query_3": {
          "statusCode": 200,
          "values": [
              [
                  "2025-04-06T00:00:00Z",
                  "2025-04-07T00:00:00Z",
                  "2025-04-08T00:00:00Z",
                  "2025-04-09T00:00:00Z",
                  "2025-04-10T00:00:00Z",
                  "2025-04-11T00:00:00Z",
                  "2025-04-12T00:00:00Z",
                  "2025-04-13T00:00:00Z"
              ],
              [
                  23443.556202,
                  22240.0,
                  22240.0,
                  56656.70023,
                  23220.0,
                  64343.227633,
                  24543.907286,
                  47875.709848
              ]
          ],
          "fields": [
              {
                  "name": "Date",
                  "displayName": "Date",
                  "typeCode": "date"
              },
              {
                  "name": "avgCostPerClickMoney",
                  "displayName": "avg CostPerClickMoney",
                  "typeCode": "decimal"
              }
          ],
          "queryId": "query_3",
          "fromDate": "2025-03-15T10:55:24.9058533+08:00",
          "toDate": "2025-04-14T10:55:24.9058533+08:00",
          "databaseQueryString": "\n                Advertising_Ad_ByDay_adroll\n             | where  Date >= datetime('03/15/2025 10:55:24 +08:00') and  Date <= datetime('04/14/2025 10:55:24 +08:00')| summarize arg_max(PDEnqueuedOn, *) by Date  | summarize avgCostPerClickMoney = avg(CostPerClickMoney)  by bin(Date, 1d)    | order by  Date asc  | project  Date, avgCostPerClickMoney| take 1000",
          "isDynamicResult": false
      }
  },
  "fromDate": "2025-03-15T10:55:24.9058533+08:00",
  "toDate": "2025-04-14T10:55:24.9058533+08:00",
  "ianaTimeZone": "Australia/Perth",
  "startOfWeek": "Sunday",
  "financialYearStart": "1/1"
};

function Startup() {

  const [ widgetDto, setWidgetDto ] = useState(widget);
  const [ rows, setRows ] = useState<QueryResponseModel | null>(data as any/*() => fakerService.getDraftResponse()*/);
  const timeoutRef = useRef<NodeJS.Timer | null>(null);
  const [ color, setColor ] = useState<string>();
  useEffect(() => {

    timeoutRef.current = setInterval(() => {
      //const data = fakerService.getDraftResponse();
      setRows(data as any);
    }, 200000);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    }
  }, []);

  return (
    <div style={{ height: "1000px", width: "1000px"}}>
      <Button onClick={() => {
        const g = {
          ...widgetDto,
          configuration: {
            ...widgetDto.configuration,
            chart: {
              ...widgetDto.configuration.chart,
              options: {
                ...widgetDto.configuration.chart.options,
                chart: {
                  ...widgetDto.configuration.chart.options.chart,
                  type: "column"
                }
              }
            }
          }
        } as WidgetDto;
        setWidgetDto(g);
      }}>Column</Button>
      <Button onClick={() => {
        const g = {
          ...widgetDto,
          configuration: {
            ...widgetDto.configuration,
            chart: {
              ...widgetDto.configuration.chart,
              options: {
                ...widgetDto.configuration.chart.options,
                chart: {
                  ...widgetDto.configuration.chart.options.chart,
                  type: "line"
                }
              }
            }
          }
        } as WidgetDto;
        setWidgetDto(g);
      }}>Line</Button>

    <Card style={{ height: "100%" }}>
      <CardContent styleName="widget-card-content">
        <Chart
            widget={{
              ...widgetDto, 
              configuration: { 
                ...widgetDto.configuration, 
                chart: { 
                  ...widgetDto.configuration.chart, 
                  options: { 
                    ...widgetDto.configuration.chart.options,
                    seriesColour: color
                  }
            }}}}
            isLoading ={false}
            isEditMode={false}
            data={rows}
            error={undefined}
        />
      </CardContent>
    </Card>
    <Form initialValues={widgetDto} onSave={v => setWidgetDto(v)} />
    </div>
  );
}

export default Startup;
