import { ChartConfigurationDto } from "redi-chart-types";
import { QueryResponse } from "redi-types";
import * as yup from "yup";

declare module "sharedwidgetcomponents/FieldFormatter" {
    export default class FieldFormatter {
        /**
         * 
         * @param type Return either string or Element type
         * @param field Response field
         * @param widget Widget settings
         * @param value Value to format
         * @param defaultFieldStandardOptions 
         * @returns Return a HTML element (either Element component or string) with a formatted value in a formatted string
         */
        static formatter<T extends "string">(type: T, field: ResponseField, widget: WidgetDto, value: string | number, defaultFieldStandardOptions?: FieldStandardOptions): string;
        static formatter<T extends "Element">(type: T, field: ResponseField, widget: WidgetDto, value: string | number, defaultFieldStandardOptions?: FieldStandardOptions): JSX.Element;
        static formatter<T extends "string" | "Element">(type: T, field: ResponseField, widget: WidgetDto, value: string | number, defaultFieldStandardOptions?: FieldStandardOptions): string | JSX.Element;
        /**
         * Helper function for formatter logic
         * @param type 
         * @param value 
         * @param field 
         * @param standardFieldOptions 
         * @returns Returns a HTML element (either Element component or string) with a formatted value in a formatted string
         */
        private static standardFieldFormatter<T extends "string" | "Element">(type: T, value: string | number, field: ResponseField, standardFieldOptions?: FieldStandardOptions): string | JSX.Element;
        /**
         * Function to return a value in a formatted string if available, otherwise the value is returned back
         * @param value Value to be inserted into format string
         * @param format (Optional) format (e.g. 'The value is {value}')
         * @returns Returns a value in a formatted string (e.g. 'The value is {value}') or just the value
         */
        static displayFormatter(value: string | number, format?: string): string | number;
        /**
         * Function to format the value based on the ValueFormatEnum passed in as an arguement
         * @param value Value to be formatted
         * @param valueFormat Value format type (ValueFormatEnum type)
         * @returns Returns a formatted value based on value format type (e.g. a Date to DD/MM/YYYY)
         */
        static valueFormatter(value: any, valueFormat?: keyof ValueFormatEnum | undefined): any;
        /**
        * Parse value to DateTime format
        * @param value Value to be parsed
        * @param format DateTime format (e.g. dd/MM/YYYY)
        * @returns 
        */
        static parseDateTimeFormat(value: any, format: string): string | null;
        /**
        * Parse value to string type
        * @param value Value to be parsed
        * @returns 
        */
        static parseString(value: any): any;
        /**
         * Parse value to boolean type
         * @param value Value to be parsed
         * @returns 
         */
        static parseBool(value: any): boolean | null;
        /**
         * Parse value to number type with optional digits
         * @param value Value to be parsed
         * @param fractionDigits Number of digits returned in the response after formatting the number
         * @returns 
         */
        static parseNumber(value: any, fractionDigits: number): any;
    };
}