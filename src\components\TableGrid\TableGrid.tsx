import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { Point } from 'highcharts';
import { useLayoutEffect, useMemo, useState } from 'react';
import { ChartConfigurationDto, WidgetDto } from 'redi-types';
import FieldFormatter from 'sharedwidgetcomponents/FieldFormatter';
import { PlottedChartOptions } from '../../hooks/usePlottedChartOptions';

const CHART_POINT_SUFFIX = "#point";
const FIELD_SUFFIX = "#field";

function getDataRows(chart?: Highcharts.Chart) {
    const list:{ category: string, [x:string] : string | number | undefined | Highcharts.Point}[] = [];
    if (chart && chart.series && chart.series[0]?.points) {
        const series = chart.series;
        const categories = chart.xAxis[0]?.categories;
        const categoryField = series[0].options.custom!.categoryField;
        for (let i = 0; i < categories.length; i++) {
            const row = { id: i, category: categories[i], [FIELD_SUFFIX]: categoryField } as { category: string, [x:string] : string | number | undefined | Highcharts.Point };
            for (let j = 0; j < series.length; j++) {
                const dataField = series[j].options.custom!.dataField;
                row[series[j].options.custom!.seriesName] = series[j].points[i]?.y ?? 0;  
                row[series[j].options.custom!.seriesName + CHART_POINT_SUFFIX] = series[j].points[i];  
                row[series[j].options.custom!.seriesName + FIELD_SUFFIX] = dataField; 
            }
            list.push(row);
        }
    }
    return list;
}

/**
 * Category - First Column
 * Series 1 - Second Column
 * Series 2 - Third Column
 * Series 3 - cont....
 * @param configuration 
 * @returns 
 */
function getColumns(widget: WidgetDto, plottedOptions?: PlottedChartOptions ): readonly GridColDef<(any)[number]>[] {
    const configuration = widget.configuration.chart;
    if (configuration && configuration.options && configuration.options.series && plottedOptions && plottedOptions.series) {
        const columns: GridColDef<(any)[number]>[] = [{
            width: undefined,
            description: undefined,
            headerName: "Category",
            sortable: false,
            field: "category",
            renderCell: (params) => {
                const row = params.row;
                const field = row[FIELD_SUFFIX];
                const defaultFieldStandardOptions = configuration.options?.xAxis?.fieldStandardOptions;
                return (
                    <div>{FieldFormatter.formatter("Element", field, widget, row["category"], defaultFieldStandardOptions)}</div>
                );
            }
        }];
        for (const series of plottedOptions.series) {

            columns.push({
                width: undefined,
                description: undefined,
                headerName: series.name,
                sortable: false,
                field: series.name!,
                renderCell: (params) => {
                    const row = params.row;
                    const cellfieldName = series.custom!.seriesName!;
                    const field = row[cellfieldName + FIELD_SUFFIX] ;
                    const defaultFieldStandardOptions = configuration.options?.yAxis?.fieldStandardOptions;
                    return (
                        <div key={"_cell_" + row.category + cellfieldName} 
                            onMouseOut={() => {
                                const point = row[cellfieldName + CHART_POINT_SUFFIX] as Highcharts.Point;
                                if (point) {
                                    point.series.chart.tooltip.hide(0);
                                }
                            }} 
                            onMouseOver={() => {  
                                const point = row[cellfieldName! + CHART_POINT_SUFFIX] as Highcharts.Point;
                                if (point) {
                                    point.series.select(true);
                                    point.series.onMouseOver();
                                    point.series.chart.tooltip.refresh(point);
                                }
                            }}
                        >
                            {FieldFormatter.formatter("Element", field, widget, row[cellfieldName], defaultFieldStandardOptions)}
                        </div>
                    )
                }
            });
        }
        return columns;    
    }
    return [];
}

function TableGrid(props: Props) {

    const {chart, plottedOptions, widget } = props;
    const [ rows, setRows ] = useState<{[x: string]: string | number | Point | undefined; category: string}[] | undefined>(undefined);
    
    useLayoutEffect(() => {
        setRows(getDataRows(chart));
    }, [plottedOptions, chart]);
    const columns = useMemo(() => getColumns(widget, plottedOptions), [plottedOptions, widget.configuration, widget.fieldPropertiesOverrides]);

    return (
        <DataGrid
            rows={rows}
            columns={columns}
            initialState={{
                pagination: {
                    paginationModel: {
                        pageSize: 100
                    },
                },
            }}
            pageSizeOptions={[5, 25, 100]}
            disableRowSelectionOnClick
        />
    );
}

export default TableGrid;

interface Props {
    plottedOptions: PlottedChartOptions | undefined;
    chart?: Highcharts.Chart;
    widget: WidgetDto;
}