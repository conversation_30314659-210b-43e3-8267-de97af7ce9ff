declare module "redi-types" {

    type ChartType = "column" | "line" | "scatter" | "pie" | "spline";

    export interface ChartConfigurationDto {
        options: ChartOptionsDto;
        isSeriesAsCategories?: boolean;
        isTableEnabled?: boolean;
        overrideOptions?: OverrideChartOptionsDto[];
    }

    export interface OverrideChartOptionsDto extends Partial<ChartOptionsDto> {
        datasetGroupByOptions?: string[];
    }

    export interface ChartOptionsDto {
        chart: ChartSettingsDto;
        xAxis: XAxisDto;
        /** Default values for yAxis */
        yAxis: YAxisDto;
        /** List of seperated yAxes to be displayed on the chart */
        yAxes?: YAxisDto[];
        legend: LegendDto;
        tooltip: TooltipDto;
        dataLabels: DataLabelDto;
        /** Matches response fields as default.*/
        series: SeriesOptionDto[];
    }

    export interface SeriesOptionDto {
        name: string;
        displayName: string;
        queryId: string;
        /** The field name that represents the categories for the return query response resultset */
        categoryFieldName?: string;
        colour?: string;
        /** The index of the yAxes to use. */
        yAxis?: number | string;
        type?: ChartType;
    }

    export interface ChartSettingsDto {
        title?: string;
        subtitle?: string;
        type?: ChartType;
        options3d?: Highcharts.Chart3dOptions;
    }

    export interface XAxisDto {
        title?: string;
        type?: "linear" | "datetime" | "logarithmic";
        isAxisShared?: boolean;
        /** Standard field options. */
        fieldStandardOptions?: FieldStandardOptions;
    }

    export interface YAxisDto {
        id?: string;
        title?: string;
        type?: "linear" | "datetime" | "logarithmic";
        opposite?: boolean;
        /** Standard field options. */
        fieldStandardOptions?: FieldStandardOptions;
    }

    export interface LegendDto {
        enabled?: boolean;
        align?: "left" | "right" | "center";
        margin?: number | "" | null;
    }

    export interface TooltipDto {
        enabled?: boolean;
    }

    export interface DataLabelDto {
        enabled?: boolean;
        align?: "left" | "right" | "center";
    }
  }