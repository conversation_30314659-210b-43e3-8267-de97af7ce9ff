export enum DateRangeEnum {
		Today = 1,
        TodayToNow = 2,
        Yesterday = 3,
        DayBeforeYesterday = 4,
        ThisDayLastWeek = 5,
        Tomorrow = 6,
        ThisWeek = 7,
        LastWeek = 8,
        NextWeek = 9,
        ThisWeekToDate = 10,
        ThisWeekLastYear = 11,
        ThisWeekToDateLastYear = 48,
        Last1Hour = 12,
        Last3Hours = 13,
        Last6Hours = 14,
        Last12Hours = 15,
        Last24Hours = 16,
        Last2Days = 17,
        Last7Days = 18,
        Last30Days = 19,
        Last60Days = 20,
        Last90Days = 21,
        Last6Months = 22,
        Last1Year = 23,
        Last2Years = 24,
        Last3Years = 25,
        Last4Years = 26,
        Last5Years = 27,
        ThisMonth = 28,
        LastMonth = 29,
        NextMonth = 30,
        ThisMonthToDate = 31,
        ThisMonthLastYear = 32,
        ThisMonthToDateLastYear = 49,
        ThisYear = 33,
        LastYear = 34,
        NextYear = 35,
        ThisYearToDate = 36,
        ThisFinancialYear = 37,
        NextFinancialYear = 39,
        ThisFinancialYearToDate = 40,
        ThisFinancialQuarter = 41,
        LastFinancialQuarter = 42,
        ThisFinancialQuarterToDate = 43,
        NextFinancialQuarter = 44,
        ThisFinancialYearQ1 = 50,
        ThisFinancialYearQ2 = 51,
        ThisFinancialYearQ3 = 52,
        ThisFinancialYearQ4 = 53,
        LastFinancialYearQ1 = 54,
        LastFinancialYearQ2 = 55,
        LastFinancialYearQ3 = 56,
        LastFinancialYearQ4 = 57,
        Last5Minutes = 45,
        Last15Minutes = 46,
        Last30Minutes = 47,
        CustomDateRange = 90,
  }
  
  