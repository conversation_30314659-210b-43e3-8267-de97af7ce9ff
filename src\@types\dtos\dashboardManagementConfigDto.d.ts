import { string } from "yup";

declare module "redi-types" {
  export interface BaseDashboardManagementConfigDto {
        dashboardId: number;
        configJson: string;
        helpText?: string;
        currentVersionNo: number;
  }

  export interface GetDashboardManagementConfigDto extends BaseDashboardManagementConfigDto {}

  export interface GetListDashboardManagementConfigDto extends BaseDashboardManagementConfigDto {}

  export interface DashboardManagementConfigHistoryDto {
    dashboardConfigHistoryId: number;
    dashboardId: number;
    configJson: string;
    createdOn: DateTime;
    createdByName?: string;
    versionNumber: number;
    note?: string;
  }
}