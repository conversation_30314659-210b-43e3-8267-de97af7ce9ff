import { useEffect } from "react";
import { ExportableHighchartsChart, ExportableWidget, WidgetDto } from "redi-types";

function useRegisterWidget(
  widget : WidgetDto,
  data : any,
  chart : Highcharts.Chart | undefined,
  registerWidget?: (widget: ExportableWidget) => void, 
  deregisterWidget? : (widgetId: string) => void, 
  exportOptions?: Highcharts.Options) {
  
  const getSVG = () : string => {
    if(!chart) {
      console.error('chart state is undefined, unable to export SVG');
      return "<svg></svg>";
    }
    const exportable = (chart as ExportableHighchartsChart);
    return exportable.getSVG!({ ...exportOptions });
  }

  useEffect(() => {
    if(chart && registerWidget) {
      registerWidget({ ...widget, getSVG });
    }
    return () => deregisterWidget && deregisterWidget(widget.id);
  }, [data, chart]);
}

export default useRegisterWidget;