'use strict';

const path = require('path');
const resolveApp = relativePath => path.resolve(__dirname.replace("config", ""), relativePath);
const getPublicUrlOrPath = require('react-dev-utils/getPublicUrlOrPath');

const dotenv = require('dotenv');
const { Exception } = require('sass');

if (process.env.SYSTEM_ENVIRONMENT === "local") {
  dotenv.config({ path: `${resolveApp('.env')}.local`});
} else if (process.env.SYSTEM_ENVIRONMENT === "development") {
  dotenv.config({ path: `${resolveApp('.env')}.development`});
} else if (process.env.SYSTEM_ENVIRONMENT === "production") {
  dotenv.config({ path: `${resolveApp('.env')}.production`});
} else {
  throw new Exception('The NODE_ENV environment variable is required but was not specified.')
}

const stringifiedEnv = {
  process: {
    env: Object.keys(process.env).reduce((env, key) => {
      env[key] = JSON.stringify(process.env[key]);
      return env;
    }, {})
  }
};

stringifiedEnv.process.env.NODE_ENV = stringifiedEnv.process.env.NODE_ENV;

const publicUrlOrPath = getPublicUrlOrPath(
  process.env.NODE_ENV === 'development',
  require(resolveApp('package.json')).homepage,
  process.env.PUBLIC_URL
);

// config after eject: we're in ./config/
module.exports = {
  dotenv: resolveApp('.env'),
  appDefinitions: resolveApp('src/@types'),
  appPath: resolveApp('.'),
  appBuild: resolveApp('build'),
  appPublic: resolveApp('public'),
  appHtml: resolveApp('public/index.html'),
  appIndexJs: resolveApp('src/index.tsx'),
  appPackageJson: resolveApp('package.json'),
  appSrc: resolveApp('src'),
  appTsConfig: resolveApp('tsconfig.json'),
  appJsConfig: resolveApp('jsconfig.json'),
  yarnLockFile: resolveApp('yarn.lock'),
  proxySetup: resolveApp('src/setupProxy.js'),
  appNodeModules: resolveApp('node_modules'),
  appWebpackCache: resolveApp('node_modules/.cache'),
  appTsBuildInfoFile: resolveApp('node_modules/.cache/tsconfig.tsbuildinfo'),//,
  stringifiedEnv,
  publicUrlOrPath
};
