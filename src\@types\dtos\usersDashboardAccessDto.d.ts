import { number, string } from "yup";

declare module "redi-types" {
  export interface BaseUsersDashboardAccessDto {
    dashboardAccessId: number;
    dashboardId: number;
    userId: string;
    dashboardAccessRoleId: number;
    tenantId: number;
  }

  export interface GetUsersDashboardAccessDto extends BaseUsersDashboardAccessDto {}

  export interface GetListUsersDashboardAccessDto extends BaseUsersDashboardAccessDto {}

  export interface GetListUserWithAccessCDto extends CurrentDetailsDto
  {
      dashboardAccessRoleId: number;
  }
}