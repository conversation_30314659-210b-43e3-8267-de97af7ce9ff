// import { Accordion, AccordionDetails, AccordionSummary, Checkbox, FormControlLabel, MenuItem, TextField, Typography } from "@mui/material";
// import { Field, useFormikContext } from "formik";
// import { ChartConfigurationDto, ChartOptionsDto, WidgetDto } from "redi-types";
// import './styles.scss';
// import { useState } from "react";

// function FormFields_OLD(props: Props) {

//     const { touched, errors } = useFormikContext<WidgetDto<ChartConfigurationDto>>();

//     const [expanded, setExpanded] = useState<string | false>('chart');

//     const handleChange = (panel: string) => (event: React.SyntheticEvent, newExpanded: boolean) => {
//       setExpanded(newExpanded ? panel : false);
//     };

//     return (
//         <>
//             <Accordion expanded={expanded === 'chart'} onChange={handleChange('chart')} style={{margin: 0}}>
//                 <AccordionSummary>
//                     <Typography>Chart</Typography>
//                 </AccordionSummary>
//                 <AccordionDetails>
//                     <div styleName="form-grid">
//                         <Field
//                             variant="standard"
//                             label="Title"
//                             id="chart-title"
//                             name={`configuration.options.chart.title`}
//                             placeholder="Title"
//                             as={TextField}
//                             error={touched.configuration?.options?.chart && touched.configuration.options.chart.title && errors.configuration?.options?.chart && Boolean(errors.configuration.options.chart.title)}
//                             helperText={touched.configuration?.options?.chart && touched.configuration.options.chart.title && errors.configuration?.options?.chart && errors.configuration.options.chart.title}
//                         />
//                         <Field
//                             variant="standard"
//                             label="Subtitle"
//                             id="chart-subtitle"
//                             name={`configuration.options.chart.subtitle`}
//                             placeholder="Subsubtitle"
//                             as={TextField}
//                             error={touched.configuration?.options?.chart && touched.configuration.options.chart.subtitle && errors.configuration?.options?.chart && Boolean(errors.configuration.options.chart.subtitle)}
//                             helperText={touched.configuration?.options?.chart && touched.configuration.options.chart.subtitle && errors.configuration?.options?.chart && errors.configuration.options.chart.subtitle}
//                         />
//                         <Field
//                             select
//                             variant="standard"
//                             label="Type"
//                             id="chart-type"
//                             name={`configuration.options.chart.type`}
//                             placeholder="Type"
//                             as={TextField}
//                             error={touched.configuration?.options?.chart && touched.configuration.options.chart.type && errors.configuration?.options?.chart && Boolean(errors.configuration.options.chart.type)}
//                             helperText={touched.configuration?.options?.chart && touched.configuration.options.chart.type && errors.configuration?.options?.chart && errors.configuration.options.chart.type}
//                         >
//                             <MenuItem key={"bar"} value={"bar"}>Bar</MenuItem>
//                             <MenuItem key={"column"} value={"column"}>Column</MenuItem>
//                             <MenuItem key={"scatter"} value={"scatter"}>Scatter</MenuItem>
//                             <MenuItem key={"pie"} value={"pie"}>Pie</MenuItem>
//                             <MenuItem key={"line"} value={"line"}>Line</MenuItem>
//                             <MenuItem key={"spline"} value={"spline"}>Spline</MenuItem>
//                             <MenuItem key={"area"} value={"area"}>Area</MenuItem>
//                         </Field>
//                         <Field
//                             id="chart-table-enabled"
//                             type="checkbox"
//                             name="configuration.isTableEnabled"
//                             as={FormControlLabel}
//                             control={<Checkbox />}
//                             label="Show table"
//                         />
//                     </div>
//                 </AccordionDetails>
//             </Accordion>
//             <Accordion expanded={expanded === 'xAxis'} onChange={handleChange('xAxis')} style={{margin: 0}}>
//                 <AccordionSummary>
//                     <Typography>XAxis</Typography>
//                 </AccordionSummary>
//                 <AccordionDetails>
                    
//                 </AccordionDetails>
//             </Accordion>
//             <Accordion expanded={expanded === 'yAxis'} onChange={handleChange('yAxis')} style={{margin: 0}}>
//                 <AccordionSummary>
//                     <Typography>YAxis</Typography>
//                 </AccordionSummary>
//                 <AccordionDetails>
                    
//                 </AccordionDetails>
//             </Accordion>
//         </>
//     );
// }

// export default FormFields_OLD;

// interface Props {}

export default {};