@import "../config/theme/vars.scss";

.header {
  background-color: #282c34;
  font-size: 1.6rem;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;

  p {
    margin: 0.5rem 1rem 0.5rem 1rem;
  }
}

.link {
  font-size: 1.3rem;
  color: white;
  padding: 0 1rem;
  &:not(:last-child) {
    border-right: 1px solid grey;
  }
}

.app-logo {
  height: 35px;
}

body {
  background-color: $pageBgColor;
}

.widget-card-content {
  height: stretch;
  flex-direction: column;
  display: flex;
}