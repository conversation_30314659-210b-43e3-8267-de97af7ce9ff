import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { ExportableWidget, WidgetProps } from 'redi-types';
import { usePlottedChartOptions } from '../../hooks/usePlottedChartOptions';
import { CircularProgress } from '@mui/material';
import { ChartSyncCrosshairs } from '../../utils/chartsynccrosshairs';
import { useEffect, useState } from 'react';
import TableGrid from '../TableGrid/TableGrid';
import { logo } from '../../assets/picardataLogoBase64';
import './styles.scss';
import { getFormattedDate } from '../../utils/datetime/formatTimeRange';
import useRegisterWidget from '../../hooks/useRegisterWidget';
import useGetWidget from '../../hooks/useGetWidget';
require("highcharts/modules/exporting.js")(Highcharts);
require("highcharts/modules/offline-exporting.js")(Highcharts); // use for local image exporting (no CORS) !!! must be imported after 'exporting.js'
require("highcharts/modules/export-data.js")(Highcharts);
require("highcharts/highcharts-3d")(Highcharts);

// Preserve dimenions with width & height as 94.4 x 45.8
// if you want to change the size of the logo, change the size var vvvvvvvvvv
const pdLogo = { url: logo, width: 94.4, height: 45.8, margin: 5, size: 0.5 };

const options = {
    title: {
        text: 'My chart',
        style: {
          fontSize: '1rem'
        }
    },
    credits: {
        enabled: false
    },
    chart: {
        options3d: {
            alpha: 15,
            beta: 15,
            depth: 50
        },
        zooming: {
            type: "x",
            singleTouch: true
        },
        className: 'disable-pd-logo'
    },
    exporting: {
        scale: 2,
        buttons: {
            contextButton: {
                menuItems: [
                    "viewFullscreen",
                    "downloadPNG",
                    "downloadJPEG",
                    "downloadSVG",
                    "downloadCSV",
                    "downloadXLS"
                ]
            }
        },
        chartOptions: {
            chart: {
                className: 'disable-pd-logo',
            },
            xAxis: {
                crosshair: false,
                plotLines: undefined
            },
            yAxis: {
                crosshair: false,
                plotLines: undefined
            }
        },
        filename: `Picardata ${getFormattedDate()}`,
        error: function(event) {
            console.error("Error exporting chart widget");
        }
    }
} as Highcharts.Options;

const chartSync = new ChartSyncCrosshairs();

/**
 * 0, 1, 3 could be index position of xAxis.categories
 *          [0, 29.9],
 *           [1, 71.5],
 *           [3, 106.4]
 * while
 *           ["a", 29.9],
 *           ["b", 71.5],
 *           ["e", 106.4]
 * could mean name of the category but not neccearily match with a category in the categories section
 * nor display as such in the x-axis
 * Plotting Series in Three Ways: https://www.highcharts.com/docs/chart-concepts/series
 * Chart Types: https://www.highcharts.com/docs/chart-and-series-types/chart-types
 * 
 **/

//Chart component when it gets new props, will re-render. If it's new data or isAxisShared changes, then a second render will happen.
function Chart(props: WidgetProps) {
    const { data, isEditMode, isLoading, error, registerWidget, deregisterWidget } = props;
    const widget = useGetWidget(props.widget);
    const configuration = widget.configuration.chart;
    const { plottedOptions } = usePlottedChartOptions(data, widget);
    const [ chart, setChart ] = useState<Highcharts.Chart>();
    const showTable = chart && configuration?.isTableEnabled && data?.results && Object.keys(data?.results).length > 0;
    
    useRegisterWidget(widget, data, chart, registerWidget, deregisterWidget, options?.exporting?.chartOptions);

    return (
        data && configuration ?
        <div style={{ height: "-webkit-fill-available", display: "grid", gridAutoRows: showTable ? "60% calc(40% - 53px)" : "100%" }}>
            <div style={{zIndex: 1, display: "grid"}}>
                <HighchartsReact
                    styleName="test-1"
                    containerProps={{
                        style: { height: "100%" }
                    }}
                    highcharts={Highcharts}
                    options={{
                        ...options,
                        title: { ...options.title, text: configuration.options.chart.title },
                        chart: {
                            //...configuration.options.chart,
                            ...options.chart,
                            options3d: {
                                ...options.chart!.options3d,
                                ...configuration.options.chart.options3d
                            },
                            events: {
                                load: function (event) {
                                    chartSync && chartSync.addCrossHairsToChart(this, event.target);
                                    setChart(this);
                                    /* invisible picardata logo in bottom right (enabled in export) */
                                    this.renderer.image(
                                      pdLogo.url,
                                      this.chartWidth - pdLogo.margin - pdLogo.width * pdLogo.size, // x
                                      this.chartHeight - pdLogo.margin - pdLogo.height * pdLogo.size, // y
                                      pdLogo.width * pdLogo.size, 
                                      pdLogo.height * pdLogo.size)
                                    .attr({ zIndex: 1000 })
                                    .add();
                                },
                                render: function (event) {
                                  // Required as exporting destroys the chart and makes a new one,
                                  // Making the chart state an empty object {}
                                  // No clue as to why, but apparently that's the behaviour of Highcharts React
                                  // This updates after the 'new' chart is created so the chart state reflects correctly
                                  setChart(this);
                                }
                            }
                        },
                        yAxis: plottedOptions?.yAxis,
                        xAxis: plottedOptions?.xAxis,
                        plotOptions: {
                            series: {
                                animation: {
                                    duration: 1000,
                                    complete: () => { }
                                }
                            }
                        },
                        tooltip: {
                            ...configuration.options.tooltip,
                            shared: true,
                            formatter: function () {
                                let text = [];
                                text.push("");
                                if (!configuration.isSeriesAsCategories && this.points) {
                                    this.points.map((point, i) => {
                                        text.push('<span style="color:' + point.series.color + ';">\u25CF\xa0</span>' + point.series.name + ' <b>' + point.y?.toLocaleString() + '</b>');
                                    });
                                } else if (this.point) {
                                    text.push('<span style="color:' + this.point.series.color + ';">\u25CF\xa0</span>' + this.point.name + ' <b>' + this.point.y?.toLocaleString() + '</b>');
                                }
                                return text.join("<br/>");
                            },
                        },
                        exporting: {
                            ...options.exporting,
                            enabled: !isEditMode,
                        },
                        legend: { ...configuration.options.legend },
                        series: plottedOptions?.series
                    } as Highcharts.Options}
                />
            </div>
            {showTable ?
                <div style={{overflow: "auto"}}>
                    <TableGrid
                        chart={chart}
                        plottedOptions={plottedOptions}
                        widget={widget}
                    />
                </div> :
            null}
        </div> : <CircularProgress size={50} />
    );
}

export default Chart;